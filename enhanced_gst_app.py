#!/usr/bin/env python3
"""
Enhanced GST Filing JSON Generator
Features:
- Improved UI with modern design
- Customer database management
- CSV upload functionality
- Invoice validation and correction
- HSN master data management
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import sqlite3
import json
import os
import csv
import re
from datetime import datetime
from typing import Dict, List, Optional, Tuple

# Database configuration
DATABASE_NAME = 'gst_data.db'
DB_PATH = os.path.join(os.path.dirname(__file__), DATABASE_NAME)

# Default values
DEFAULT_HSN = "998892"
DEFAULT_RATE = 5.0
DEFAULT_POS = "27"
DEFAULT_INV_TYPE = "R"
DEFAULT_RCHRG = "N"
DEFAULT_SUPPLY_TYPE = "INTRA"
DEFAULT_B2CS_TYPE = "OE"

class DatabaseManager:
    """Handles all database operations"""

    @staticmethod
    def create_connection():
        """Create a database connection to the SQLite database"""
        conn = None
        try:
            conn = sqlite3.connect(DB_PATH)
            conn.row_factory = sqlite3.Row  # Return rows as dictionary-like objects
            return conn
        except sqlite3.Error as e:
            print(f"Database Error: {e}")  # Print instead of showing messagebox during startup
            return None

    @staticmethod
    def safe_get_row_value(row, column_name, default_value=''):
        """Safely get value from sqlite3.Row object"""
        try:
            if column_name in row.keys():
                return row[column_name] if row[column_name] is not None else default_value
            else:
                return default_value
        except (KeyError, IndexError, TypeError):
            return default_value

    @staticmethod
    def execute_query(query: str, params: tuple = (), fetch: str = None):
        """Execute a database query with error handling"""
        conn = DatabaseManager.create_connection()
        if conn is None:
            return None

        try:
            cursor = conn.cursor()
            cursor.execute(query, params)

            if fetch == 'one':
                result = cursor.fetchone()
            elif fetch == 'all':
                result = cursor.fetchall()
            else:
                result = cursor.rowcount

            conn.commit()
            return result
        except sqlite3.Error as e:
            conn.rollback()
            print(f"Database Error: {e}")  # Print to console instead of showing messagebox
            return None
        finally:
            if conn:
                conn.close()

    @staticmethod
    def safe_execute_query(query: str, params: tuple = (), fetch: str = None, default_value=None):
        """Execute a database query with safe error handling"""
        try:
            result = DatabaseManager.execute_query(query, params, fetch)
            return result if result is not None else default_value
        except Exception as e:
            print(f"Safe query error: {e}")
            return default_value

class ValidationManager:
    """Handles validation of GST data"""

    @staticmethod
    def validate_gstin(gstin: str) -> Tuple[bool, str]:
        """Validate GSTIN format"""
        if not gstin:
            return False, "GSTIN is required"

        if len(gstin) != 15:
            return False, "GSTIN must be 15 characters long"

        # Basic GSTIN format validation
        pattern = r'^[0-9]{2}[A-Z]{5}[0-9]{4}[A-Z]{1}[1-9A-Z]{1}[Z]{1}[0-9A-Z]{1}$'
        if not re.match(pattern, gstin):
            return False, "Invalid GSTIN format"

        return True, "Valid GSTIN"
    
    @staticmethod
    def validate_invoice_date(date_str: str) -> Tuple[bool, str]:
        """Validate invoice date format"""
        try:
            datetime.strptime(date_str, '%d-%m-%Y')
            return True, "Valid date"
        except ValueError:
            return False, "Invalid date format. Use DD-MM-YYYY"
    
    @staticmethod
    def validate_hsn_code(hsn_code: str) -> Tuple[bool, str]:
        """Validate HSN code format"""
        if not hsn_code:
            return False, "HSN code is required"
        
        if len(hsn_code) not in [4, 6, 8]:
            return False, "HSN code must be 4, 6, or 8 digits"
        
        if not hsn_code.isdigit():
            return False, "HSN code must contain only digits"

        return True, "Valid HSN code"

    @staticmethod
    def validate_quarter(quarter_str: str) -> Tuple[bool, str]:
        """Validate quarter format (Q1YYYY, Q2YYYY, Q3YYYY, Q4YYYY)"""
        if not quarter_str:
            return False, "Quarter is required"

        if len(quarter_str) != 6:
            return False, "Quarter must be in format Q1YYYY, Q2YYYY, Q3YYYY, or Q4YYYY"

        if not quarter_str.startswith('Q'):
            return False, "Quarter must start with 'Q'"

        quarter_num = quarter_str[1]
        if quarter_num not in ['1', '2', '3', '4']:
            return False, "Quarter number must be 1, 2, 3, or 4"

        year_part = quarter_str[2:]
        if not year_part.isdigit() or len(year_part) != 4:
            return False, "Year must be 4 digits"

        return True, "Valid quarter format"

class QuarterManager:
    """Handles quarterly GST filing periods"""

    @staticmethod
    def get_quarter_months(quarter: str) -> List[str]:
        """Get list of months (MMYYYY format) for a given quarter"""
        if len(quarter) != 6 or not quarter.startswith('Q'):
            return []

        quarter_num = quarter[1]
        year = quarter[2:]

        quarter_months = {
            '1': ['04', '05', '06'],  # Q1: Apr-Jun
            '2': ['07', '08', '09'],  # Q2: Jul-Sep
            '3': ['10', '11', '12'],  # Q3: Oct-Dec
            '4': ['01', '02', '03']   # Q4: Jan-Mar (next year for Q4)
        }

        if quarter_num not in quarter_months:
            return []

        months = quarter_months[quarter_num]

        # Handle Q4 which spans across years
        if quarter_num == '4':
            next_year = str(int(year) + 1)
            return [f"{month}{next_year}" for month in months]
        else:
            return [f"{month}{year}" for month in months]

    @staticmethod
    def get_quarter_from_month(month_year: str) -> str:
        """Get quarter (Q1YYYY format) from month (MMYYYY format)"""
        if len(month_year) != 6 or not month_year.isdigit():
            return ""

        month = month_year[:2]
        year = month_year[2:]

        # Determine quarter based on month
        if month in ['04', '05', '06']:
            return f"Q1{year}"
        elif month in ['07', '08', '09']:
            return f"Q2{year}"
        elif month in ['10', '11', '12']:
            return f"Q3{year}"
        elif month in ['01', '02', '03']:
            # For Q4, the year is the previous year
            prev_year = str(int(year) - 1)
            return f"Q4{prev_year}"

        return ""

    @staticmethod
    def get_current_quarter() -> str:
        """Get current quarter in Q1YYYY format"""
        now = datetime.now()
        month = now.strftime('%m')
        year = now.strftime('%Y')
        month_year = f"{month}{year}"
        return QuarterManager.get_quarter_from_month(month_year)

    @staticmethod
    def get_quarter_display_name(quarter: str) -> str:
        """Get display name for quarter"""
        if len(quarter) != 6 or not quarter.startswith('Q'):
            return quarter

        quarter_num = quarter[1]
        year = quarter[2:]

        quarter_names = {
            '1': f"Q1 {year} (Apr-Jun {year})",
            '2': f"Q2 {year} (Jul-Sep {year})",
            '3': f"Q3 {year} (Oct-Dec {year})",
            '4': f"Q4 {year} (Jan-Mar {int(year)+1})"
        }

        return quarter_names.get(quarter_num, quarter)

    @staticmethod
    def get_filing_period_for_quarter(quarter: str) -> str:
        """Get the filing period (last month of quarter) for JSON generation"""
        months = QuarterManager.get_quarter_months(quarter)
        if not months:
            return ""

        # Return the last month of the quarter for the filing period
        return months[-1]

class JSONGenerator:
    """Handles GST JSON generation for quarterly returns"""

    @staticmethod
    def fetch_supplier_info():
        """Fetch supplier information"""
        supplier = DatabaseManager.safe_execute_query(
            "SELECT * FROM SupplierInfo LIMIT 1",
            fetch='one'
        )
        return supplier

    @staticmethod
    def fetch_invoices_for_quarter(quarter: str):
        """Fetch all invoices for a given quarter"""
        months = QuarterManager.get_quarter_months(quarter)
        if not months:
            return [], {}

        # Create placeholders for IN clause
        placeholders = ','.join(['?' for _ in months])

        # Fetch invoices for all months in the quarter with customer details
        invoices = DatabaseManager.safe_execute_query(
            f"""SELECT inv.*,
                       cust.name as customer_name,
                       cust.gstin as customer_gstin,
                       cust.state_code as customer_state_code
                FROM Invoices inv
                LEFT JOIN Customers cust ON inv.customer_id = cust.id
                WHERE inv.filing_period IN ({placeholders})
                ORDER BY inv.invoice_date, inv.invoice_number""",
            tuple(months),
            fetch='all',
            default_value=[]
        )

        if not invoices:
            return [], {}

        # Fetch invoice items
        invoice_ids = [inv['id'] for inv in invoices]
        items_placeholders = ','.join(['?' for _ in invoice_ids])

        items = DatabaseManager.safe_execute_query(
            f"""SELECT * FROM InvoiceItems
                WHERE invoice_id IN ({items_placeholders})
                ORDER BY invoice_id, id""",
            tuple(invoice_ids),
            fetch='all',
            default_value=[]
        )

        # Group items by invoice_id
        items_map = {}
        for item in items:
            inv_id = item['invoice_id']
            if inv_id not in items_map:
                items_map[inv_id] = []
            items_map[inv_id].append(dict(item))

        return [dict(inv) for inv in invoices], items_map

    @staticmethod
    def generate_gstr1_json(quarter: str, include_hsn: bool = False):
        """Generate GSTR-1 JSON for a quarter"""
        # Validate quarter format
        is_valid, error_msg = ValidationManager.validate_quarter(quarter)
        if not is_valid:
            return None, f"Invalid quarter format: {error_msg}"

        # Get supplier info
        supplier_info = JSONGenerator.fetch_supplier_info()
        if not supplier_info or not DatabaseManager.safe_get_row_value(supplier_info, 'gstin'):
            return None, "Supplier GSTIN not found. Please save Supplier Info first."

        # Fetch invoices for the quarter
        invoices, items_map = JSONGenerator.fetch_invoices_for_quarter(quarter)
        if not invoices:
            return None, f"No invoices found for quarter {quarter}."

        # Get filing period (last month of quarter)
        filing_period = QuarterManager.get_filing_period_for_quarter(quarter)

        # Initialize JSON structure
        output_json = {
            "gstin": DatabaseManager.safe_get_row_value(supplier_info, 'gstin'),
            "fp": filing_period,  # Use last month of quarter
            "version": "GST3.2",
            "hash": "hash",  # Placeholder
            "b2b": []
        }

        # Process B2B invoices
        b2b_customers = {}
        b2c_items = []
        all_items = []  # For HSN summary

        for invoice in invoices:
            invoice_items = items_map.get(invoice['id'], [])

            # Calculate total invoice value
            total_value = sum(
                item['taxable_value'] + item['cgst_amount'] + item['sgst_amount'] + item['cess_amount']
                for item in invoice_items
            )

            # Format invoice date
            try:
                inv_date = datetime.strptime(invoice['invoice_date'], '%Y-%m-%d')
                formatted_date = inv_date.strftime('%d-%m-%Y')
            except ValueError:
                formatted_date = invoice['invoice_date']

            if invoice['invoice_type'] == 'B2B':
                # Group by customer GSTIN
                customer_gstin = DatabaseManager.safe_get_row_value(invoice, 'customer_gstin', 'UNREGISTERED')

                if customer_gstin not in b2b_customers:
                    b2b_customers[customer_gstin] = {
                        "ctin": customer_gstin,
                        "inv": []
                    }

                # Create invoice object
                invoice_obj = {
                    "inum": invoice['invoice_number'],
                    "idt": formatted_date,
                    "val": round(total_value, 2),
                    "pos": DatabaseManager.safe_get_row_value(invoice, 'place_of_supply', '27'),
                    "rchrg": DatabaseManager.safe_get_row_value(invoice, 'reverse_charge', 'N'),
                    "inv_typ": DatabaseManager.safe_get_row_value(invoice, 'invoice_type_gstr', 'R'),
                    "itms": []
                }

                # Add items
                for idx, item in enumerate(invoice_items, 1):
                    invoice_obj["itms"].append({
                        "num": idx,
                        "itm_det": {
                            "txval": round(item['taxable_value'], 2),
                            "rt": round(item['rate'], 2),
                            "camt": round(item['cgst_amount'], 2),
                            "samt": round(item['sgst_amount'], 2),
                            "csamt": round(item['cess_amount'], 2)
                        }
                    })

                    # Add to all items for HSN summary
                    all_items.append(item)

                b2b_customers[customer_gstin]["inv"].append(invoice_obj)

            elif invoice['invoice_type'] == 'B2C':
                # Add items to B2C list
                for item in invoice_items:
                    b2c_items.append({
                        'pos': invoice['place_of_supply'],
                        'rate': item['rate'],
                        'taxable_value': item['taxable_value'],
                        'cgst_amount': item['cgst_amount'],
                        'sgst_amount': item['sgst_amount'],
                        'cess_amount': item['cess_amount']
                    })

                    # Add to all items for HSN summary
                    all_items.append(item)

        # Add B2B data to JSON
        output_json["b2b"] = list(b2b_customers.values())

        # Process B2C data if exists
        if b2c_items:
            b2cs_summary = {}

            for item in b2c_items:
                key = (item['pos'], item['rate'])

                if key not in b2cs_summary:
                    b2cs_summary[key] = {
                        "sply_ty": "INTRA",
                        "rt": round(item['rate'], 2),
                        "typ": "OE",
                        "pos": item['pos'],
                        "txval": 0.0,
                        "camt": 0.0,
                        "samt": 0.0,
                        "csamt": 0.0
                    }

                b2cs_summary[key]["txval"] += item['taxable_value']
                b2cs_summary[key]["camt"] += item['cgst_amount']
                b2cs_summary[key]["samt"] += item['sgst_amount']
                b2cs_summary[key]["csamt"] += item['cess_amount']

            # Round B2C summary values
            for summary in b2cs_summary.values():
                summary["txval"] = round(summary["txval"], 2)
                summary["camt"] = round(summary["camt"], 2)
                summary["samt"] = round(summary["samt"], 2)
                summary["csamt"] = round(summary["csamt"], 2)

            output_json["b2cs"] = list(b2cs_summary.values())

        # Process HSN summary if requested
        if include_hsn and all_items:
            hsn_summary = {}

            for item in all_items:
                hsn_code = item['hsn_code']

                if hsn_code not in hsn_summary:
                    hsn_summary[hsn_code] = {
                        "hsn_sc": hsn_code,
                        "desc": "",
                        "uqc": "NOS",
                        "qty": 0,
                        "rt": round(item['rate'], 2),
                        "txval": 0.0,
                        "iamt": 0.0,
                        "camt": 0.0,
                        "samt": 0.0,
                        "csamt": 0.0
                    }

                hsn_summary[hsn_code]["txval"] += item['taxable_value']
                hsn_summary[hsn_code]["camt"] += item['cgst_amount']
                hsn_summary[hsn_code]["samt"] += item['sgst_amount']
                hsn_summary[hsn_code]["csamt"] += item['cess_amount']

            # Round HSN summary values and add sequence numbers
            hsn_data = []
            for idx, (hsn_code, summary) in enumerate(hsn_summary.items(), 1):
                summary["num"] = idx
                summary["txval"] = round(summary["txval"], 2)
                summary["camt"] = round(summary["camt"], 2)
                summary["samt"] = round(summary["samt"], 2)
                summary["csamt"] = round(summary["csamt"], 2)
                hsn_data.append(summary)

            output_json["hsn"] = {"data": hsn_data}

        return output_json, None

class ModernUI:
    """Modern UI styling and components"""
    
    @staticmethod
    def configure_styles():
        """Configure modern ttk styles"""
        style = ttk.Style()
        
        # Configure modern button style
        style.configure('Modern.TButton',
                       padding=(10, 5),
                       font=('Segoe UI', 9))
        
        # Configure header label style
        style.configure('Header.TLabel',
                       font=('Segoe UI', 12, 'bold'),
                       foreground='#2c3e50')
        
        # Configure subheader label style
        style.configure('Subheader.TLabel',
                       font=('Segoe UI', 10, 'bold'),
                       foreground='#34495e')
        
        # Configure entry style
        style.configure('Modern.TEntry',
                       padding=5,
                       font=('Segoe UI', 9))
        
        # Configure frame style
        style.configure('Card.TFrame',
                       relief='solid',
                       borderwidth=1,
                       background='#ffffff')

    @staticmethod
    def create_card_frame(parent, title: str = None) -> ttk.Frame:
        """Create a modern card-style frame"""
        frame = ttk.Frame(parent, style='Card.TFrame', padding=15)
        
        if title:
            title_label = ttk.Label(frame, text=title, style='Subheader.TLabel')
            title_label.pack(anchor='w', pady=(0, 10))
        
        return frame

    @staticmethod
    def create_form_field(parent, label_text: str, entry_width: int = 30, 
                         is_required: bool = False) -> Tuple[ttk.Label, ttk.Entry]:
        """Create a form field with label and entry"""
        field_frame = ttk.Frame(parent)
        field_frame.pack(fill='x', pady=2)
        
        # Add asterisk for required fields
        display_text = f"{label_text}*" if is_required else label_text
        label = ttk.Label(field_frame, text=display_text, width=20, anchor='w')
        label.pack(side='left', padx=(0, 10))
        
        entry = ttk.Entry(field_frame, width=entry_width, style='Modern.TEntry')
        entry.pack(side='left', fill='x', expand=True)
        
        return label, entry

class EnhancedGSTApp(tk.Tk):
    """Enhanced GST Filing Application with modern UI"""
    
    def __init__(self):
        super().__init__()
        self.title("Enhanced GST Filing JSON Generator v2.0")
        self.geometry("1200x800")
        self.minsize(1000, 600)
        
        # Configure modern styles
        ModernUI.configure_styles()
        
        # Initialize variables
        self.selected_customer_id = None
        self.customer_map = {}
        self.hsn_map = {}
        self.state_map = {}
        
        # Create main interface
        self.create_main_interface()
        
        # Load initial data
        self.load_master_data()
    
    def create_main_interface(self):
        """Create the main application interface"""
        # Main container with padding
        main_container = ttk.Frame(self, padding=10)
        main_container.pack(fill='both', expand=True)
        
        # Title header
        title_frame = ttk.Frame(main_container)
        title_frame.pack(fill='x', pady=(0, 20))
        
        title_label = ttk.Label(title_frame, 
                               text="Enhanced GST Filing JSON Generator", 
                               style='Header.TLabel')
        title_label.pack(side='left')
        
        # Version label
        version_label = ttk.Label(title_frame, text="v2.0", 
                                 font=('Segoe UI', 8), 
                                 foreground='#7f8c8d')
        version_label.pack(side='right')
        
        # Create notebook for tabs
        self.notebook = ttk.Notebook(main_container)
        self.notebook.pack(fill='both', expand=True)
        
        # Create tab frames
        self.create_tabs()
    
    def create_tabs(self):
        """Create all application tabs"""
        # Tab 1: Dashboard
        self.dashboard_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.dashboard_frame, text='📊 Dashboard')
        
        # Tab 2: Customer Management
        self.customer_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.customer_frame, text='👥 Customers')
        
        # Tab 3: CSV Upload
        self.csv_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.csv_frame, text='📁 CSV Upload')
        
        # Tab 4: Invoice Management
        self.invoice_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.invoice_frame, text='📄 Invoices')
        
        # Tab 5: Validation & Correction
        self.validation_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.validation_frame, text='✅ Validation')
        
        # Tab 6: JSON Generation
        self.generate_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.generate_frame, text='⚙️ Generate JSON')
        
        # Tab 7: Settings
        self.settings_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.settings_frame, text='⚙️ Settings')
        
        # Populate tabs
        self.create_dashboard_tab()
        self.create_customer_tab()
        self.create_csv_tab()
        self.create_invoice_tab()
        self.create_validation_tab()
        self.create_generate_tab()
        self.create_settings_tab()
    
    def load_master_data(self):
        """Load master data from database"""
        # Load HSN master data
        hsn_data = DatabaseManager.execute_query(
            "SELECT hsn_code, description, gst_rate FROM HsnMaster WHERE is_active = 1",
            fetch='all'
        )
        if hsn_data:
            self.hsn_map = {row['hsn_code']: dict(row) for row in hsn_data}

        # Load state master data
        state_data = DatabaseManager.execute_query(
            "SELECT state_code, state_name FROM StateMaster WHERE is_active = 1",
            fetch='all'
        )
        if state_data:
            self.state_map = {row['state_code']: row['state_name'] for row in state_data}

    def create_dashboard_tab(self):
        """Create dashboard tab with overview statistics"""
        # Main container with scrollable frame
        canvas = tk.Canvas(self.dashboard_frame)
        scrollbar = ttk.Scrollbar(self.dashboard_frame, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas)

        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        # Dashboard content
        dashboard_content = ttk.Frame(scrollable_frame, padding=20)
        dashboard_content.pack(fill='both', expand=True)

        # Welcome section
        welcome_card = ModernUI.create_card_frame(dashboard_content, "Welcome to Enhanced GST Filing Tool")
        welcome_card.pack(fill='x', pady=(0, 20))

        welcome_text = ttk.Label(welcome_card,
                                text="Manage your GST filing process with enhanced features including CSV upload, validation, and customer management.",
                                font=('Segoe UI', 10),
                                wraplength=800)
        welcome_text.pack(anchor='w')

        # Statistics cards row
        stats_frame = ttk.Frame(dashboard_content)
        stats_frame.pack(fill='x', pady=(0, 20))

        # Customer count card
        customer_card = ModernUI.create_card_frame(stats_frame, "Total Customers")
        customer_card.pack(side='left', fill='both', expand=True, padx=(0, 10))

        self.customer_count_label = ttk.Label(customer_card, text="Loading...",
                                             font=('Segoe UI', 16, 'bold'),
                                             foreground='#3498db')
        self.customer_count_label.pack()

        # Invoice count card
        invoice_card = ModernUI.create_card_frame(stats_frame, "Total Invoices")
        invoice_card.pack(side='left', fill='both', expand=True, padx=(5, 5))

        self.invoice_count_label = ttk.Label(invoice_card, text="Loading...",
                                            font=('Segoe UI', 16, 'bold'),
                                            foreground='#e74c3c')
        self.invoice_count_label.pack()

        # Pending validation card
        validation_card = ModernUI.create_card_frame(stats_frame, "Pending Validation")
        validation_card.pack(side='left', fill='both', expand=True, padx=(5, 0))

        self.pending_count_label = ttk.Label(validation_card, text="Loading...",
                                            font=('Segoe UI', 16, 'bold'),
                                            foreground='#f39c12')
        self.pending_count_label.pack()

        # Recent activity section
        activity_card = ModernUI.create_card_frame(dashboard_content, "Recent Activity")
        activity_card.pack(fill='both', expand=True)

        # Activity list
        self.activity_tree = ttk.Treeview(activity_card,
                                         columns=('Date', 'Action', 'Details'),
                                         show='headings',
                                         height=8)

        self.activity_tree.heading('Date', text='Date')
        self.activity_tree.heading('Action', text='Action')
        self.activity_tree.heading('Details', text='Details')

        self.activity_tree.column('Date', width=120)
        self.activity_tree.column('Action', width=150)
        self.activity_tree.column('Details', width=400)

        self.activity_tree.pack(fill='both', expand=True, pady=(10, 0))

        # Refresh dashboard data
        self.refresh_dashboard()

    def create_customer_tab(self):
        """Create enhanced customer management tab"""
        # Main container
        main_container = ttk.Frame(self.customer_frame, padding=10)
        main_container.pack(fill='both', expand=True)

        # Top section with search and add button
        top_frame = ttk.Frame(main_container)
        top_frame.pack(fill='x', pady=(0, 10))

        # Search section
        search_frame = ttk.Frame(top_frame)
        search_frame.pack(side='left', fill='x', expand=True)

        ttk.Label(search_frame, text="Search Customers:").pack(side='left', padx=(0, 5))
        self.customer_search_var = tk.StringVar()
        self.customer_search_entry = ttk.Entry(search_frame, textvariable=self.customer_search_var, width=30)
        self.customer_search_entry.pack(side='left', padx=(0, 10))
        self.customer_search_entry.bind('<KeyRelease>', self.on_customer_search)

        search_btn = ttk.Button(search_frame, text="🔍 Search", command=self.search_customers)
        search_btn.pack(side='left', padx=(0, 10))

        clear_search_btn = ttk.Button(search_frame, text="Clear", command=self.clear_customer_search)
        clear_search_btn.pack(side='left')

        # Add customer button
        add_customer_btn = ttk.Button(top_frame, text="➕ Add New Customer",
                                     command=self.open_customer_form,
                                     style='Modern.TButton')
        add_customer_btn.pack(side='right')

        # Customer list section
        list_frame = ttk.Frame(main_container)
        list_frame.pack(fill='both', expand=True)

        # Customer treeview
        columns = ('ID', 'Name', 'GSTIN', 'State', 'Phone', 'Email', 'Status')
        self.customer_tree = ttk.Treeview(list_frame, columns=columns, show='headings', height=15)

        # Configure columns
        self.customer_tree.heading('ID', text='ID')
        self.customer_tree.heading('Name', text='Customer Name')
        self.customer_tree.heading('GSTIN', text='GSTIN')
        self.customer_tree.heading('State', text='State')
        self.customer_tree.heading('Phone', text='Phone')
        self.customer_tree.heading('Email', text='Email')
        self.customer_tree.heading('Status', text='Status')

        self.customer_tree.column('ID', width=50)
        self.customer_tree.column('Name', width=200)
        self.customer_tree.column('GSTIN', width=150)
        self.customer_tree.column('State', width=100)
        self.customer_tree.column('Phone', width=120)
        self.customer_tree.column('Email', width=180)
        self.customer_tree.column('Status', width=80)

        # Scrollbars for treeview
        v_scrollbar = ttk.Scrollbar(list_frame, orient='vertical', command=self.customer_tree.yview)
        h_scrollbar = ttk.Scrollbar(list_frame, orient='horizontal', command=self.customer_tree.xview)
        self.customer_tree.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)

        # Pack treeview and scrollbars
        self.customer_tree.grid(row=0, column=0, sticky='nsew')
        v_scrollbar.grid(row=0, column=1, sticky='ns')
        h_scrollbar.grid(row=1, column=0, sticky='ew')

        list_frame.grid_rowconfigure(0, weight=1)
        list_frame.grid_columnconfigure(0, weight=1)

        # Context menu for customer actions
        self.customer_context_menu = tk.Menu(self, tearoff=0)
        self.customer_context_menu.add_command(label="Edit Customer", command=self.edit_selected_customer)
        self.customer_context_menu.add_command(label="View Details", command=self.view_customer_details)
        self.customer_context_menu.add_separator()
        self.customer_context_menu.add_command(label="Deactivate", command=self.deactivate_customer)
        self.customer_context_menu.add_command(label="Delete", command=self.delete_customer)

        self.customer_tree.bind("<Button-3>", self.show_customer_context_menu)
        self.customer_tree.bind("<Double-1>", self.edit_selected_customer)

        # Load customer data
        self.refresh_customer_list()

    def create_csv_tab(self):
        """Create CSV upload and processing tab with quarter validation"""
        # Main container
        main_container = ttk.Frame(self.csv_frame, padding=20)
        main_container.pack(fill='both', expand=True)

        # Title
        title_label = ttk.Label(main_container, text="CSV Upload & Import",
                               style='Header.TLabel')
        title_label.pack(pady=(0, 20))

        # Quarter Selection Section
        quarter_card = ModernUI.create_card_frame(main_container, "Target Quarter & Financial Year")
        quarter_card.pack(fill='x', pady=(0, 20))

        quarter_info = ttk.Label(quarter_card,
                                text="Select the quarter and financial year for which you're uploading invoice data.",
                                font=('Segoe UI', 10))
        quarter_info.pack(anchor='w', pady=(0, 10))

        # Quarter selection frame
        quarter_select_frame = ttk.Frame(quarter_card)
        quarter_select_frame.pack(fill='x', pady=(0, 10))

        ttk.Label(quarter_select_frame, text="Target Quarter:*",
                 font=('Segoe UI', 10, 'bold')).pack(side='left', padx=(0, 10))

        self.upload_quarter_var = tk.StringVar(value=QuarterManager.get_current_quarter())
        self.upload_quarter_combo = ttk.Combobox(quarter_select_frame, textvariable=self.upload_quarter_var,
                                                width=15, state='readonly')
        self.upload_quarter_combo.pack(side='left', padx=(0, 20))

        # Populate quarter dropdown
        self.populate_upload_quarter_dropdown()

        # Quarter info display
        self.upload_quarter_info_label = ttk.Label(quarter_select_frame, text="",
                                                  font=('Segoe UI', 9),
                                                  foreground='#7f8c8d')
        self.upload_quarter_info_label.pack(side='left')

        # Update quarter info when selection changes
        self.upload_quarter_combo.bind('<<ComboboxSelected>>', self.update_upload_quarter_info)
        self.update_upload_quarter_info()

        # Upload section
        upload_card = ModernUI.create_card_frame(main_container, "CSV File Upload")
        upload_card.pack(fill='x', pady=(0, 20))

        # File selection
        file_frame = ttk.Frame(upload_card)
        file_frame.pack(fill='x', pady=(0, 10))

        ttk.Label(file_frame, text="Select CSV File:").pack(side='left', padx=(0, 10))
        self.csv_file_var = tk.StringVar()
        self.csv_file_entry = ttk.Entry(file_frame, textvariable=self.csv_file_var,
                                       state='readonly', width=50)
        self.csv_file_entry.pack(side='left', fill='x', expand=True, padx=(0, 10))

        browse_btn = ttk.Button(file_frame, text="📁 Browse", command=self.browse_csv_file)
        browse_btn.pack(side='right')

        # Upload options
        options_frame = ttk.Frame(upload_card)
        options_frame.pack(fill='x', pady=(0, 10))

        self.validate_on_upload = tk.BooleanVar(value=True)
        validate_cb = ttk.Checkbutton(options_frame, text="Validate data on upload",
                                     variable=self.validate_on_upload)
        validate_cb.pack(side='left', padx=(0, 20))

        self.validate_quarter_dates = tk.BooleanVar(value=True)
        quarter_validate_cb = ttk.Checkbutton(options_frame, text="Validate dates against target quarter",
                                             variable=self.validate_quarter_dates)
        quarter_validate_cb.pack(side='left', padx=(0, 20))

        self.skip_duplicates = tk.BooleanVar(value=True)
        skip_cb = ttk.Checkbutton(options_frame, text="Skip duplicate invoices",
                                 variable=self.skip_duplicates)
        skip_cb.pack(side='left')

        # Upload button
        upload_btn = ttk.Button(upload_card, text="⬆️ Upload and Process CSV",
                               command=self.upload_csv_file,
                               style='Modern.TButton')
        upload_btn.pack(pady=(10, 0))

        # CSV Template section
        template_card = ModernUI.create_card_frame(main_container, "CSV Template")
        template_card.pack(fill='x', pady=(0, 20))

        template_text = ttk.Label(template_card,
                                 text="Download the CSV template to ensure proper data format.",
                                 font=('Segoe UI', 10))
        template_text.pack(anchor='w', pady=(0, 10))

        template_btn = ttk.Button(template_card, text="📥 Download CSV Template",
                                 command=self.download_csv_template)
        template_btn.pack(anchor='w')

        # Upload history section
        history_card = ModernUI.create_card_frame(main_container, "Upload History")
        history_card.pack(fill='both', expand=True)

        # History treeview
        history_columns = ('Date', 'Filename', 'Total', 'Valid', 'Invalid', 'Status')
        self.upload_history_tree = ttk.Treeview(history_card, columns=history_columns,
                                               show='headings', height=10)

        for col in history_columns:
            self.upload_history_tree.heading(col, text=col)
            self.upload_history_tree.column(col, width=120)

        # Scrollbar for history
        history_scrollbar = ttk.Scrollbar(history_card, orient='vertical',
                                         command=self.upload_history_tree.yview)
        self.upload_history_tree.configure(yscrollcommand=history_scrollbar.set)

        self.upload_history_tree.pack(side='left', fill='both', expand=True, pady=(10, 0))
        history_scrollbar.pack(side='right', fill='y', pady=(10, 0))

        # Load upload history
        self.refresh_upload_history()

    def create_invoice_tab(self):
        """Create invoice management tab"""
        # Placeholder for invoice management
        placeholder_frame = ttk.Frame(self.invoice_frame, padding=20)
        placeholder_frame.pack(fill='both', expand=True)

        ttk.Label(placeholder_frame, text="Invoice Management",
                 style='Header.TLabel').pack(pady=(0, 20))

        ttk.Label(placeholder_frame,
                 text="This tab will contain invoice management features including manual entry, editing, and bulk operations.",
                 font=('Segoe UI', 10)).pack()

    def create_validation_tab(self):
        """Create validation and correction tab"""
        # Placeholder for validation features
        placeholder_frame = ttk.Frame(self.validation_frame, padding=20)
        placeholder_frame.pack(fill='both', expand=True)

        ttk.Label(placeholder_frame, text="Invoice Validation & Correction",
                 style='Header.TLabel').pack(pady=(0, 20))

        ttk.Label(placeholder_frame,
                 text="This tab will contain validation rules, error correction interface, and data quality checks.",
                 font=('Segoe UI', 10)).pack()

    def create_generate_tab(self):
        """Create JSON generation tab with quarterly support"""
        # Main container
        main_container = ttk.Frame(self.generate_frame, padding=20)
        main_container.pack(fill='both', expand=True)

        # Title
        title_label = ttk.Label(main_container, text="GST JSON Generation (Quarterly)",
                               style='Header.TLabel')
        title_label.pack(pady=(0, 20))

        # Options card
        options_card = ModernUI.create_card_frame(main_container, "Generation Options")
        options_card.pack(fill='x', pady=(0, 20))

        # Quarter selection
        quarter_frame = ttk.Frame(options_card)
        quarter_frame.pack(fill='x', pady=(0, 10))

        ttk.Label(quarter_frame, text="Select Quarter:*", font=('Segoe UI', 10, 'bold')).pack(side='left', padx=(0, 10))

        self.quarter_var = tk.StringVar(value=QuarterManager.get_current_quarter())
        self.quarter_combo = ttk.Combobox(quarter_frame, textvariable=self.quarter_var,
                                         width=15, state='readonly')
        self.quarter_combo.pack(side='left', padx=(0, 20))

        # Populate quarter dropdown
        self.populate_quarter_dropdown()

        # Quarter info label
        self.quarter_info_label = ttk.Label(quarter_frame, text="",
                                           font=('Segoe UI', 9),
                                           foreground='#7f8c8d')
        self.quarter_info_label.pack(side='left')

        # Update quarter info when selection changes
        self.quarter_combo.bind('<<ComboboxSelected>>', self.update_quarter_info)
        self.update_quarter_info()

        # Options checkboxes
        options_frame = ttk.Frame(options_card)
        options_frame.pack(fill='x', pady=(0, 10))

        self.include_hsn_var = tk.BooleanVar(value=True)
        hsn_check = ttk.Checkbutton(options_frame, text="Include HSN Summary",
                                   variable=self.include_hsn_var)
        hsn_check.pack(side='left', padx=(0, 20))

        self.validate_data_var = tk.BooleanVar(value=True)
        validate_check = ttk.Checkbutton(options_frame, text="Validate data before generation",
                                        variable=self.validate_data_var)
        validate_check.pack(side='left')

        # Action buttons
        button_frame = ttk.Frame(options_card)
        button_frame.pack(fill='x', pady=(10, 0))

        generate_btn = ttk.Button(button_frame, text="🔄 Generate JSON",
                                 command=self.generate_quarterly_json,
                                 style='Modern.TButton')
        generate_btn.pack(side='left', padx=(0, 10))

        preview_btn = ttk.Button(button_frame, text="👁️ Preview Data",
                                command=self.preview_quarter_data)
        preview_btn.pack(side='left', padx=(0, 10))

        save_btn = ttk.Button(button_frame, text="💾 Save JSON to File",
                             command=self.save_json_to_file)
        save_btn.pack(side='right')

        # JSON output section
        output_card = ModernUI.create_card_frame(main_container, "Generated JSON")
        output_card.pack(fill='both', expand=True)

        # JSON text area with scrollbars
        text_frame = ttk.Frame(output_card)
        text_frame.pack(fill='both', expand=True, pady=(10, 0))

        self.json_output_text = tk.Text(text_frame, wrap=tk.NONE,
                                       font=('Consolas', 9),
                                       bg='#f8f9fa', fg='#2c3e50')

        # Scrollbars
        v_scrollbar = ttk.Scrollbar(text_frame, orient='vertical', command=self.json_output_text.yview)
        h_scrollbar = ttk.Scrollbar(text_frame, orient='horizontal', command=self.json_output_text.xview)

        self.json_output_text.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)

        # Grid layout for text and scrollbars
        self.json_output_text.grid(row=0, column=0, sticky='nsew')
        v_scrollbar.grid(row=0, column=1, sticky='ns')
        h_scrollbar.grid(row=1, column=0, sticky='ew')

        text_frame.grid_rowconfigure(0, weight=1)
        text_frame.grid_columnconfigure(0, weight=1)

        # Status bar
        self.json_status_label = ttk.Label(output_card, text="Ready to generate JSON",
                                          font=('Segoe UI', 9),
                                          foreground='#27ae60')
        self.json_status_label.pack(pady=(10, 0))

    def create_settings_tab(self):
        """Create settings and configuration tab with supplier info"""
        # Main container with scrollable frame
        canvas = tk.Canvas(self.settings_frame)
        scrollbar = ttk.Scrollbar(self.settings_frame, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas)

        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        # Settings content
        settings_content = ttk.Frame(scrollable_frame, padding=20)
        settings_content.pack(fill='both', expand=True)

        # Title
        title_label = ttk.Label(settings_content, text="Settings & Configuration",
                               style='Header.TLabel')
        title_label.pack(pady=(0, 30))

        # Supplier Information Section
        supplier_card = ModernUI.create_card_frame(settings_content, "Supplier Information (Your Company Details)")
        supplier_card.pack(fill='x', pady=(0, 20))

        # Supplier form
        supplier_form = ttk.Frame(supplier_card)
        supplier_form.pack(fill='x', pady=(10, 0))

        # GSTIN (Required)
        self.supplier_gstin_label, self.supplier_gstin_entry = ModernUI.create_form_field(
            supplier_form, "GSTIN", is_required=True)

        # Legal Name
        self.supplier_legal_label, self.supplier_legal_entry = ModernUI.create_form_field(
            supplier_form, "Legal Name", is_required=True)

        # Trade Name
        self.supplier_trade_label, self.supplier_trade_entry = ModernUI.create_form_field(
            supplier_form, "Trade Name")

        # Address Line 1
        self.supplier_addr1_label, self.supplier_addr1_entry = ModernUI.create_form_field(
            supplier_form, "Address Line 1")

        # Address Line 2
        self.supplier_addr2_label, self.supplier_addr2_entry = ModernUI.create_form_field(
            supplier_form, "Address Line 2")

        # City
        self.supplier_city_label, self.supplier_city_entry = ModernUI.create_form_field(
            supplier_form, "City")

        # State
        self.supplier_state_label, self.supplier_state_entry = ModernUI.create_form_field(
            supplier_form, "State")

        # Pincode
        self.supplier_pincode_label, self.supplier_pincode_entry = ModernUI.create_form_field(
            supplier_form, "Pincode")

        # Supplier buttons
        supplier_btn_frame = ttk.Frame(supplier_card)
        supplier_btn_frame.pack(fill='x', pady=(20, 0))

        save_supplier_btn = ttk.Button(supplier_btn_frame, text="💾 Save Supplier Info",
                                      command=self.save_supplier_info,
                                      style='Modern.TButton')
        save_supplier_btn.pack(side='left', padx=(0, 10))

        load_supplier_btn = ttk.Button(supplier_btn_frame, text="🔄 Load Current Info",
                                      command=self.load_supplier_info)
        load_supplier_btn.pack(side='left')

        # HSN Master Management Section
        hsn_card = ModernUI.create_card_frame(settings_content, "HSN Master Management")
        hsn_card.pack(fill='x', pady=(0, 20))

        hsn_info = ttk.Label(hsn_card,
                            text="Manage HSN codes, descriptions, and default tax rates for your business.",
                            font=('Segoe UI', 10))
        hsn_info.pack(anchor='w', pady=(0, 10))

        hsn_btn_frame = ttk.Frame(hsn_card)
        hsn_btn_frame.pack(fill='x')

        manage_hsn_btn = ttk.Button(hsn_btn_frame, text="🏷️ Manage HSN Codes",
                                   command=self.open_hsn_manager)
        manage_hsn_btn.pack(side='left', padx=(0, 10))

        import_hsn_btn = ttk.Button(hsn_btn_frame, text="📥 Import HSN Data",
                                   command=self.import_hsn_data)
        import_hsn_btn.pack(side='left')

        # Application Settings Section
        app_card = ModernUI.create_card_frame(settings_content, "Application Settings")
        app_card.pack(fill='x', pady=(0, 20))

        # Default values section
        defaults_frame = ttk.LabelFrame(app_card, text="Default Values", padding=10)
        defaults_frame.pack(fill='x', pady=(10, 0))

        # Default HSN
        default_hsn_frame = ttk.Frame(defaults_frame)
        default_hsn_frame.pack(fill='x', pady=2)
        ttk.Label(default_hsn_frame, text="Default HSN Code:", width=20).pack(side='left')
        self.default_hsn_var = tk.StringVar(value=DEFAULT_HSN)
        default_hsn_entry = ttk.Entry(default_hsn_frame, textvariable=self.default_hsn_var, width=15)
        default_hsn_entry.pack(side='left', padx=(10, 0))

        # Default Rate
        default_rate_frame = ttk.Frame(defaults_frame)
        default_rate_frame.pack(fill='x', pady=2)
        ttk.Label(default_rate_frame, text="Default Tax Rate (%):", width=20).pack(side='left')
        self.default_rate_var = tk.StringVar(value=str(DEFAULT_RATE))
        default_rate_entry = ttk.Entry(default_rate_frame, textvariable=self.default_rate_var, width=15)
        default_rate_entry.pack(side='left', padx=(10, 0))

        # Default POS
        default_pos_frame = ttk.Frame(defaults_frame)
        default_pos_frame.pack(fill='x', pady=2)
        ttk.Label(default_pos_frame, text="Default Place of Supply:", width=20).pack(side='left')
        self.default_pos_var = tk.StringVar(value=DEFAULT_POS)
        default_pos_entry = ttk.Entry(default_pos_frame, textvariable=self.default_pos_var, width=15)
        default_pos_entry.pack(side='left', padx=(10, 0))

        # Database Management Section
        db_card = ModernUI.create_card_frame(settings_content, "Database Management")
        db_card.pack(fill='x')

        db_info = ttk.Label(db_card,
                           text="Backup, restore, and manage your GST data.",
                           font=('Segoe UI', 10))
        db_info.pack(anchor='w', pady=(0, 10))

        db_btn_frame = ttk.Frame(db_card)
        db_btn_frame.pack(fill='x')

        backup_btn = ttk.Button(db_btn_frame, text="💾 Backup Database",
                               command=self.backup_database)
        backup_btn.pack(side='left', padx=(0, 10))

        restore_btn = ttk.Button(db_btn_frame, text="📂 Restore Database",
                                command=self.restore_database)
        restore_btn.pack(side='left', padx=(0, 10))

        reset_btn = ttk.Button(db_btn_frame, text="🗑️ Reset Database",
                              command=self.reset_database)
        reset_btn.pack(side='right')

        # Load current supplier info
        self.load_supplier_info()

    # Supplier Information Management Methods
    def load_supplier_info(self):
        """Load current supplier information"""
        supplier = DatabaseManager.safe_execute_query(
            "SELECT * FROM SupplierInfo LIMIT 1",
            fetch='one'
        )

        if supplier:
            # Clear existing entries
            for entry in [self.supplier_gstin_entry, self.supplier_legal_entry,
                         self.supplier_trade_entry, self.supplier_addr1_entry,
                         self.supplier_addr2_entry, self.supplier_city_entry,
                         self.supplier_state_entry, self.supplier_pincode_entry]:
                entry.delete(0, tk.END)

            # Load data
            self.supplier_gstin_entry.insert(0, DatabaseManager.safe_get_row_value(supplier, 'gstin', ''))
            self.supplier_legal_entry.insert(0, DatabaseManager.safe_get_row_value(supplier, 'legal_name', ''))
            self.supplier_trade_entry.insert(0, DatabaseManager.safe_get_row_value(supplier, 'trade_name', ''))
            self.supplier_addr1_entry.insert(0, DatabaseManager.safe_get_row_value(supplier, 'address1', ''))
            self.supplier_addr2_entry.insert(0, DatabaseManager.safe_get_row_value(supplier, 'address2', ''))
            self.supplier_city_entry.insert(0, DatabaseManager.safe_get_row_value(supplier, 'city', ''))
            self.supplier_state_entry.insert(0, DatabaseManager.safe_get_row_value(supplier, 'state', ''))
            self.supplier_pincode_entry.insert(0, DatabaseManager.safe_get_row_value(supplier, 'pincode', ''))

    def save_supplier_info(self):
        """Save supplier information"""
        # Validate required fields
        gstin = self.supplier_gstin_entry.get().strip()
        legal_name = self.supplier_legal_entry.get().strip()

        if not gstin:
            messagebox.showerror("Validation Error", "GSTIN is required.")
            return

        if not legal_name:
            messagebox.showerror("Validation Error", "Legal Name is required.")
            return

        # Validate GSTIN format
        is_valid, error_msg = ValidationManager.validate_gstin(gstin)
        if not is_valid:
            messagebox.showerror("Validation Error", f"GSTIN Error: {error_msg}")
            return

        # Prepare supplier data
        supplier_data = {
            'gstin': gstin,
            'legal_name': legal_name,
            'trade_name': self.supplier_trade_entry.get().strip(),
            'address1': self.supplier_addr1_entry.get().strip(),
            'address2': self.supplier_addr2_entry.get().strip(),
            'city': self.supplier_city_entry.get().strip(),
            'state': self.supplier_state_entry.get().strip(),
            'pincode': self.supplier_pincode_entry.get().strip()
        }

        try:
            # Check if supplier info exists
            existing = DatabaseManager.safe_execute_query(
                "SELECT id FROM SupplierInfo LIMIT 1",
                fetch='one'
            )

            if existing:
                # Update existing supplier
                DatabaseManager.execute_query(
                    """UPDATE SupplierInfo
                       SET gstin=?, legal_name=?, trade_name=?, address1=?, address2=?,
                           city=?, state=?, pincode=?, updated_at=CURRENT_TIMESTAMP
                       WHERE id=?""",
                    (*supplier_data.values(), existing['id'])
                )
                messagebox.showinfo("Success", "Supplier information updated successfully.")
            else:
                # Create new supplier
                DatabaseManager.execute_query(
                    """INSERT INTO SupplierInfo
                       (gstin, legal_name, trade_name, address1, address2, city, state, pincode)
                       VALUES (?, ?, ?, ?, ?, ?, ?, ?)""",
                    tuple(supplier_data.values())
                )
                messagebox.showinfo("Success", "Supplier information saved successfully.")

            # Refresh dashboard to reflect changes
            self.refresh_dashboard()

        except Exception as e:
            messagebox.showerror("Database Error", f"Failed to save supplier information: {e}")

    def open_hsn_manager(self):
        """Open HSN code management window"""
        messagebox.showinfo("HSN Manager", "HSN Manager window will be implemented in the next update.\n\nFor now, you can:\n• View HSN codes in the database\n• Add new HSN codes manually\n• Import HSN data via CSV")

    def import_hsn_data(self):
        """Import HSN data from file"""
        file_path = filedialog.askopenfilename(
            title="Select HSN Data File",
            filetypes=[("CSV files", "*.csv"), ("Excel files", "*.xlsx"), ("All files", "*.*")]
        )

        if file_path:
            messagebox.showinfo("Import HSN", f"HSN import functionality will be implemented.\nSelected file: {file_path}")

    def backup_database(self):
        """Backup database to file"""
        import shutil

        backup_path = filedialog.asksaveasfilename(
            title="Save Database Backup",
            defaultextension=".db",
            filetypes=[("Database files", "*.db"), ("All files", "*.*")],
            initialfile=f"gst_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.db"
        )

        if backup_path:
            try:
                shutil.copy2(DB_PATH, backup_path)
                messagebox.showinfo("Success", f"Database backed up to {backup_path}")
            except Exception as e:
                messagebox.showerror("Backup Error", f"Failed to backup database: {e}")

    def restore_database(self):
        """Restore database from backup"""
        import shutil

        if messagebox.askyesno("Confirm Restore",
                              "This will replace your current database with the backup.\n\nAre you sure you want to continue?"):

            backup_path = filedialog.askopenfilename(
                title="Select Database Backup",
                filetypes=[("Database files", "*.db"), ("All files", "*.*")]
            )

            if backup_path:
                try:
                    shutil.copy2(backup_path, DB_PATH)
                    messagebox.showinfo("Success", "Database restored successfully. Please restart the application.")
                except Exception as e:
                    messagebox.showerror("Restore Error", f"Failed to restore database: {e}")

    def reset_database(self):
        """Reset database to initial state"""
        if messagebox.askyesno("Confirm Reset",
                              "This will delete ALL your data and reset the database to initial state.\n\nThis action cannot be undone. Are you sure?"):

            if messagebox.askyesno("Final Confirmation",
                                  "Last chance! This will permanently delete:\n• All customers\n• All invoices\n• All supplier info\n• All uploaded data\n\nProceed with reset?"):
                try:
                    # Close any existing connections
                    conn = DatabaseManager.create_connection()
                    if conn:
                        conn.close()

                    # Delete database file
                    if os.path.exists(DB_PATH):
                        os.remove(DB_PATH)

                    # Recreate database
                    from database_setup import setup_database
                    setup_database()

                    messagebox.showinfo("Success", "Database reset successfully. Please restart the application.")

                except Exception as e:
                    messagebox.showerror("Reset Error", f"Failed to reset database: {e}")

    # Dashboard methods
    def refresh_dashboard(self):
        """Refresh dashboard statistics"""
        try:
            # Get customer count (handle missing is_active column)
            customer_count = DatabaseManager.safe_execute_query(
                "SELECT COUNT(*) as count FROM Customers WHERE is_active = 1",
                fetch='one',
                default_value=None
            )

            if customer_count is None:
                # Fallback if is_active column doesn't exist
                customer_count = DatabaseManager.safe_execute_query(
                    "SELECT COUNT(*) as count FROM Customers",
                    fetch='one',
                    default_value={'count': 0}
                )

            self.customer_count_label.config(text=str(customer_count['count']) if customer_count else "0")

            # Get invoice count
            invoice_count = DatabaseManager.safe_execute_query(
                "SELECT COUNT(*) as count FROM Invoices",
                fetch='one',
                default_value={'count': 0}
            )
            self.invoice_count_label.config(text=str(invoice_count['count']) if invoice_count else "0")

            # Get pending validation count (handle missing validation_status column)
            pending_count = DatabaseManager.safe_execute_query(
                "SELECT COUNT(*) as count FROM Invoices WHERE validation_status = 'PENDING'",
                fetch='one',
                default_value=None
            )

            if pending_count is None:
                # Fallback if validation_status column doesn't exist
                pending_count = {'count': 0}

            self.pending_count_label.config(text=str(pending_count['count']) if pending_count else "0")

        except Exception as e:
            print(f"Error refreshing dashboard: {e}")
            # Set default values on error
            self.customer_count_label.config(text="0")
            self.invoice_count_label.config(text="0")
            self.pending_count_label.config(text="0")

    # Customer management methods
    def refresh_customer_list(self):
        """Refresh the customer list in the treeview"""
        # Clear existing items
        for item in self.customer_tree.get_children():
            self.customer_tree.delete(item)

        # Try to fetch customers with new columns first
        customers = DatabaseManager.safe_execute_query(
            """SELECT id, name, gstin, state_code, phone, email, is_active
               FROM Customers ORDER BY name""",
            fetch='all'
        )

        # If that fails, try with basic columns
        if customers is None:
            customers = DatabaseManager.safe_execute_query(
                """SELECT id, name, gstin, state_code, address1, address2, city, pincode
                   FROM Customers ORDER BY name""",
                fetch='all',
                default_value=[]
            )

        if customers:
            for customer in customers:
                # Handle missing columns gracefully using helper function
                phone = DatabaseManager.safe_get_row_value(customer, 'phone', '')
                email = DatabaseManager.safe_get_row_value(customer, 'email', '')
                is_active = DatabaseManager.safe_get_row_value(customer, 'is_active', 1)

                status = "Active" if is_active else "Inactive"
                state_name = self.state_map.get(customer['state_code'], customer['state_code'])

                self.customer_tree.insert('', tk.END, values=(
                    customer['id'],
                    customer['name'],
                    customer['gstin'] or '',
                    f"{customer['state_code']} - {state_name}",
                    phone,
                    email,
                    status
                ))

    def on_customer_search(self, event=None):
        """Handle customer search as user types"""
        search_term = self.customer_search_var.get().lower()

        # Clear current items
        for item in self.customer_tree.get_children():
            self.customer_tree.delete(item)

        if not search_term:
            self.refresh_customer_list()
            return

        # Try search with new columns first
        customers = DatabaseManager.safe_execute_query(
            """SELECT id, name, gstin, state_code, phone, email, is_active
               FROM Customers
               WHERE LOWER(name) LIKE ? OR LOWER(gstin) LIKE ? OR LOWER(phone) LIKE ?
               ORDER BY name""",
            (f'%{search_term}%', f'%{search_term}%', f'%{search_term}%'),
            fetch='all'
        )

        # If that fails, try basic search
        if customers is None:
            customers = DatabaseManager.safe_execute_query(
                """SELECT id, name, gstin, state_code
                   FROM Customers
                   WHERE LOWER(name) LIKE ? OR LOWER(gstin) LIKE ?
                   ORDER BY name""",
                (f'%{search_term}%', f'%{search_term}%'),
                fetch='all',
                default_value=[]
            )

        if customers:
            for customer in customers:
                # Handle missing columns gracefully using helper function
                phone = DatabaseManager.safe_get_row_value(customer, 'phone', '')
                email = DatabaseManager.safe_get_row_value(customer, 'email', '')
                is_active = DatabaseManager.safe_get_row_value(customer, 'is_active', 1)

                status = "Active" if is_active else "Inactive"
                state_name = self.state_map.get(customer['state_code'], customer['state_code'])

                self.customer_tree.insert('', tk.END, values=(
                    customer['id'],
                    customer['name'],
                    customer['gstin'] or '',
                    f"{customer['state_code']} - {state_name}",
                    phone,
                    email,
                    status
                ))

    def search_customers(self):
        """Perform customer search"""
        self.on_customer_search()

    def clear_customer_search(self):
        """Clear customer search and refresh list"""
        self.customer_search_var.set("")
        self.refresh_customer_list()

    def open_customer_form(self, customer_id=None):
        """Open customer form for adding/editing"""
        CustomerFormWindow(self, customer_id)

    def edit_selected_customer(self, event=None):
        """Edit the selected customer"""
        selection = self.customer_tree.selection()
        if not selection:
            messagebox.showwarning("Selection Error", "Please select a customer to edit.")
            return

        item = self.customer_tree.item(selection[0])
        customer_id = item['values'][0]
        self.open_customer_form(customer_id)

    def view_customer_details(self):
        """View detailed customer information"""
        selection = self.customer_tree.selection()
        if not selection:
            messagebox.showwarning("Selection Error", "Please select a customer to view.")
            return

        item = self.customer_tree.item(selection[0])
        customer_id = item['values'][0]

        # Fetch detailed customer data
        customer = DatabaseManager.execute_query(
            "SELECT * FROM Customers WHERE id = ?",
            (customer_id,),
            fetch='one'
        )

        if customer:
            CustomerDetailsWindow(self, customer)

    def show_customer_context_menu(self, event):
        """Show context menu for customer actions"""
        # Select the item under cursor
        item = self.customer_tree.identify_row(event.y)
        if item:
            self.customer_tree.selection_set(item)
            self.customer_context_menu.post(event.x_root, event.y_root)

    def deactivate_customer(self):
        """Deactivate selected customer"""
        selection = self.customer_tree.selection()
        if not selection:
            return

        item = self.customer_tree.item(selection[0])
        customer_id = item['values'][0]
        customer_name = item['values'][1]

        if messagebox.askyesno("Confirm Deactivation",
                              f"Are you sure you want to deactivate customer '{customer_name}'?"):
            DatabaseManager.execute_query(
                "UPDATE Customers SET is_active = 0, updated_at = CURRENT_TIMESTAMP WHERE id = ?",
                (customer_id,)
            )
            self.refresh_customer_list()
            messagebox.showinfo("Success", f"Customer '{customer_name}' has been deactivated.")

    def delete_customer(self):
        """Delete selected customer"""
        selection = self.customer_tree.selection()
        if not selection:
            return

        item = self.customer_tree.item(selection[0])
        customer_id = item['values'][0]
        customer_name = item['values'][1]

        # Check if customer has invoices
        invoice_count = DatabaseManager.execute_query(
            "SELECT COUNT(*) as count FROM Invoices WHERE customer_id = ?",
            (customer_id,),
            fetch='one'
        )

        if invoice_count and invoice_count['count'] > 0:
            messagebox.showerror("Cannot Delete",
                               f"Customer '{customer_name}' has {invoice_count['count']} associated invoices and cannot be deleted.")
            return

        if messagebox.askyesno("Confirm Deletion",
                              f"Are you sure you want to permanently delete customer '{customer_name}'?\n\nThis action cannot be undone."):
            DatabaseManager.execute_query(
                "DELETE FROM Customers WHERE id = ?",
                (customer_id,)
            )
            self.refresh_customer_list()
            messagebox.showinfo("Success", f"Customer '{customer_name}' has been deleted.")

    # CSV processing methods
    def browse_csv_file(self):
        """Browse and select CSV file"""
        file_path = filedialog.askopenfilename(
            title="Select CSV File",
            filetypes=[("CSV files", "*.csv"), ("All files", "*.*")]
        )
        if file_path:
            self.csv_file_var.set(file_path)

    def download_csv_template(self):
        """Download CSV template file"""
        template_data = [
            ['invoice_type', 'customer_name', 'customer_gstin', 'customer_state_code',
             'invoice_number', 'invoice_date', 'place_of_supply', 'hsn_code',
             'description', 'taxable_value', 'rate', 'cgst_amount', 'sgst_amount', 'cess_amount'],
            ['B2B', 'Sample Customer Ltd', '27AABCS1234E1ZF', '27',
             'INV001', '01-01-2025', '27', '998892',
             'Sample Service', '1000.00', '18.00', '90.00', '90.00', '0.00'],
            ['B2C', '', '', '27',
             'INV002', '02-01-2025', '27', '998892',
             'Sample Product', '500.00', '5.00', '12.50', '12.50', '0.00']
        ]

        file_path = filedialog.asksaveasfilename(
            title="Save CSV Template",
            defaultextension=".csv",
            filetypes=[("CSV files", "*.csv")]
        )

        if file_path:
            try:
                with open(file_path, 'w', newline='', encoding='utf-8') as csvfile:
                    writer = csv.writer(csvfile)
                    writer.writerows(template_data)
                messagebox.showinfo("Success", f"CSV template saved to {file_path}")
            except Exception as e:
                messagebox.showerror("Error", f"Failed to save template: {e}")

    def upload_csv_file(self):
        """Process and upload CSV file"""
        file_path = self.csv_file_var.get()
        if not file_path:
            messagebox.showwarning("File Error", "Please select a CSV file first.")
            return

        if not os.path.exists(file_path):
            messagebox.showerror("File Error", "Selected file does not exist.")
            return

        try:
            # Create upload record
            upload_id = DatabaseManager.execute_query(
                """INSERT INTO CsvUploads (filename, file_path, status)
                   VALUES (?, ?, 'PROCESSING')""",
                (os.path.basename(file_path), file_path)
            )

            if not upload_id:
                messagebox.showerror("Database Error", "Failed to create upload record.")
                return

            # Process CSV file
            self.process_csv_file(file_path, upload_id)

        except Exception as e:
            messagebox.showerror("Upload Error", f"Failed to process CSV file: {e}")

    def process_csv_file(self, file_path: str, upload_id: int):
        """Process CSV file and import data"""
        try:
            total_records = 0
            processed_records = 0
            valid_records = 0
            invalid_records = 0
            errors = []

            with open(file_path, 'r', encoding='utf-8') as csvfile:
                # Detect delimiter
                sample = csvfile.read(1024)
                csvfile.seek(0)
                sniffer = csv.Sniffer()
                delimiter = sniffer.sniff(sample).delimiter

                reader = csv.DictReader(csvfile, delimiter=delimiter)

                for row_num, row in enumerate(reader, start=2):  # Start from 2 (header is row 1)
                    total_records += 1

                    try:
                        # Validate and process row
                        if self.validate_csv_row(row, row_num):
                            self.import_csv_row(row, upload_id)
                            valid_records += 1
                        else:
                            invalid_records += 1

                        processed_records += 1

                    except Exception as e:
                        invalid_records += 1
                        errors.append(f"Row {row_num}: {str(e)}")

            # Update upload record
            status = "COMPLETED" if invalid_records == 0 else "COMPLETED_WITH_ERRORS"
            DatabaseManager.execute_query(
                """UPDATE CsvUploads
                   SET total_records = ?, processed_records = ?, valid_records = ?,
                       invalid_records = ?, status = ?, error_log = ?
                   WHERE id = ?""",
                (total_records, processed_records, valid_records, invalid_records,
                 status, json.dumps(errors), upload_id)
            )

            # Show results
            messagebox.showinfo("Upload Complete",
                              f"CSV processing completed!\n\n"
                              f"Total records: {total_records}\n"
                              f"Valid records: {valid_records}\n"
                              f"Invalid records: {invalid_records}")

            # Refresh upload history
            self.refresh_upload_history()

        except Exception as e:
            # Update upload record with error
            DatabaseManager.execute_query(
                "UPDATE CsvUploads SET status = 'FAILED', error_log = ? WHERE id = ?",
                (json.dumps([str(e)]), upload_id)
            )
            raise e

    def validate_csv_row(self, row: dict, row_num: int) -> bool:
        """Validate a single CSV row"""
        required_fields = ['invoice_type', 'invoice_number', 'invoice_date',
                          'taxable_value', 'rate']

        # Check required fields
        for field in required_fields:
            if not row.get(field, '').strip():
                return False

        # Validate invoice type
        if row['invoice_type'].upper() not in ['B2B', 'B2C']:
            return False

        # Validate date format
        is_valid, _ = ValidationManager.validate_invoice_date(row['invoice_date'])
        if not is_valid:
            return False

        # Validate numeric fields
        try:
            float(row['taxable_value'])
            float(row['rate'])
        except ValueError:
            return False

        return True

    def import_csv_row(self, row: dict, upload_id: int, target_quarter: str = None):
        """Import a single CSV row into database"""
        # Process customer data for B2B invoices
        customer_id = None
        if row['invoice_type'].upper() == 'B2B':
            customer_gstin = row.get('customer_gstin', '').strip()
            customer_name = row.get('customer_name', '').strip()
            customer_state = row.get('customer_state_code', DEFAULT_POS).strip()

            if customer_gstin:
                # Check if customer exists by GSTIN first
                existing_customer = DatabaseManager.safe_execute_query(
                    "SELECT id FROM Customers WHERE gstin = ?",
                    (customer_gstin,),
                    fetch='one'
                )

                if existing_customer:
                    customer_id = existing_customer['id']
                else:
                    # Create new customer with GSTIN
                    # If no customer_name provided, use a default based on GSTIN
                    if not customer_name:
                        customer_name = f"Customer_{customer_gstin[:8]}"

                    customer_id = DatabaseManager.execute_query(
                        """INSERT INTO Customers (name, gstin, state_code)
                           VALUES (?, ?, ?)""",
                        (customer_name, customer_gstin, customer_state)
                    )
            elif customer_name:
                # Fallback: check by name if no GSTIN provided
                existing_customer = DatabaseManager.safe_execute_query(
                    "SELECT id FROM Customers WHERE name = ?",
                    (customer_name,),
                    fetch='one'
                )

                if existing_customer:
                    customer_id = existing_customer['id']
                else:
                    # Create new customer without GSTIN
                    customer_id = DatabaseManager.execute_query(
                        """INSERT INTO Customers (name, gstin, state_code)
                           VALUES (?, ?, ?)""",
                        (customer_name, None, customer_state)
                    )

        # Convert date format
        invoice_date = datetime.strptime(row['invoice_date'], '%d-%m-%Y').strftime('%Y-%m-%d')

        # Create invoice with all required fields
        invoice_id = DatabaseManager.execute_query(
            """INSERT INTO Invoices (invoice_type, customer_id, invoice_number, invoice_date,
                                   place_of_supply, invoice_type_gstr, reverse_charge,
                                   filing_period, source, csv_upload_id)
               VALUES (?, ?, ?, ?, ?, ?, ?, ?, 'CSV_UPLOAD', ?)""",
            (row['invoice_type'].upper(), customer_id, row['invoice_number'],
             invoice_date, row.get('place_of_supply', DEFAULT_POS),
             row.get('invoice_type_gstr', 'R'), row.get('reverse_charge', 'N'),
             datetime.strptime(row['invoice_date'], '%d-%m-%Y').strftime('%m%Y'),
             upload_id)
        )

        # Create invoice item
        if invoice_id:
            DatabaseManager.execute_query(
                """INSERT INTO InvoiceItems (invoice_id, hsn_code, description, taxable_value,
                                           rate, cgst_amount, sgst_amount, cess_amount)
                   VALUES (?, ?, ?, ?, ?, ?, ?, ?)""",
                (invoice_id, row.get('hsn_code', DEFAULT_HSN), row.get('description', ''),
                 float(row['taxable_value']), float(row['rate']),
                 float(row.get('cgst_amount', 0)), float(row.get('sgst_amount', 0)),
                 float(row.get('cess_amount', 0)))
            )

    def refresh_upload_history(self):
        """Refresh CSV upload history"""
        # Clear existing items
        for item in self.upload_history_tree.get_children():
            self.upload_history_tree.delete(item)

        # Fetch upload history
        uploads = DatabaseManager.execute_query(
            """SELECT upload_date, filename, total_records, valid_records,
                      invalid_records, status
               FROM CsvUploads ORDER BY upload_date DESC LIMIT 50""",
            fetch='all'
        )

        if uploads:
            for upload in uploads:
                upload_date = datetime.fromisoformat(upload['upload_date']).strftime('%Y-%m-%d %H:%M')
                self.upload_history_tree.insert('', tk.END, values=(
                    upload_date,
                    upload['filename'],
                    upload['total_records'] or 0,
                    upload['valid_records'] or 0,
                    upload['invalid_records'] or 0,
                    upload['status']
                ))

    # CSV Upload Quarter Validation Methods
    def populate_upload_quarter_dropdown(self):
        """Populate quarter dropdown for CSV upload"""
        current_year = datetime.now().year
        quarters = []

        # Generate quarters for current and previous 2 years
        for year in range(current_year - 2, current_year + 1):
            for q in range(1, 5):
                quarters.append(f"Q{q}{year}")

        self.upload_quarter_combo['values'] = quarters

    def update_upload_quarter_info(self, event=None):
        """Update quarter information display for upload"""
        quarter = self.upload_quarter_var.get()
        if quarter:
            display_name = QuarterManager.get_quarter_display_name(quarter)
            months = QuarterManager.get_quarter_months(quarter)
            month_info = f"Months: {', '.join(months)}"
            self.upload_quarter_info_label.config(text=f"{display_name} | {month_info}")
        else:
            self.upload_quarter_info_label.config(text="")

    def validate_invoice_against_quarter(self, invoice_date: str, target_quarter: str) -> tuple[bool, str]:
        """Validate if invoice date falls within target quarter"""
        try:
            # Parse invoice date
            inv_date = datetime.strptime(invoice_date, '%d-%m-%Y')
            month_year = inv_date.strftime('%m%Y')

            # Get target quarter months
            target_months = QuarterManager.get_quarter_months(target_quarter)

            if month_year in target_months:
                return True, f"Invoice date {invoice_date} is valid for {target_quarter}"
            else:
                # Determine which quarter this invoice belongs to
                actual_quarter = QuarterManager.get_quarter_from_month(month_year)
                return False, f"Invoice date {invoice_date} belongs to {actual_quarter}, not {target_quarter}"

        except ValueError as e:
            return False, f"Invalid date format: {invoice_date}"

    def show_quarter_validation_results(self, validation_results: dict):
        """Show quarter validation results to user"""
        if not validation_results['mismatched_invoices']:
            messagebox.showinfo("Quarter Validation",
                              f"✅ All {validation_results['total_invoices']} invoices are valid for {validation_results['target_quarter']}")
            return True

        # Show validation results window
        return self.show_quarter_validation_dialog(validation_results)

    def pre_validate_csv_quarters(self, file_path: str, target_quarter: str) -> dict:
        """Pre-validate CSV file against target quarter"""
        validation_results = {
            'target_quarter': target_quarter,
            'total_invoices': 0,
            'valid_invoices': 0,
            'mismatched_invoices': []
        }

        try:
            with open(file_path, 'r', encoding='utf-8') as csvfile:
                reader = csv.DictReader(csvfile)

                for row_num, row in enumerate(reader, start=2):
                    validation_results['total_invoices'] += 1

                    invoice_date = row.get('invoice_date', '')
                    if invoice_date:
                        is_valid, message = self.validate_invoice_against_quarter(invoice_date, target_quarter)

                        if is_valid:
                            validation_results['valid_invoices'] += 1
                        else:
                            validation_results['mismatched_invoices'].append({
                                'row': row_num,
                                'invoice_number': row.get('invoice_number', 'Unknown'),
                                'invoice_date': invoice_date,
                                'message': message
                            })

        except Exception as e:
            print(f"Error during pre-validation: {e}")

        return validation_results

    def show_quarter_validation_dialog(self, validation_results: dict) -> bool:
        """Show quarter validation dialog and get user decision"""
        mismatched = validation_results['mismatched_invoices']
        target_quarter = validation_results['target_quarter']

        if not mismatched:
            messagebox.showinfo("Quarter Validation",
                              f"✅ All {validation_results['total_invoices']} invoices are valid for {target_quarter}")
            return True

        # Create validation message
        message = f"""Quarter Validation Results for {QuarterManager.get_quarter_display_name(target_quarter)}:

📊 Total Invoices: {validation_results['total_invoices']}
✅ Valid for Quarter: {validation_results['valid_invoices']}
⚠️ Mismatched: {len(mismatched)}

Mismatched Invoices:
"""

        # Show first few mismatches
        for i, mismatch in enumerate(mismatched[:5]):
            message += f"\n• Row {mismatch['row']}: {mismatch['invoice_number']} ({mismatch['invoice_date']})"

        if len(mismatched) > 5:
            message += f"\n... and {len(mismatched) - 5} more"

        message += f"""

Do you want to proceed with the upload?
• YES: Import all invoices (mismatched ones will be flagged)
• NO: Cancel upload and review your data"""

        return messagebox.askyesno("Quarter Validation", message)

    # JSON Generation methods
    def populate_quarter_dropdown(self):
        """Populate quarter dropdown with available quarters"""
        current_year = datetime.now().year
        quarters = []

        # Generate quarters for current and previous 2 years
        for year in range(current_year - 2, current_year + 1):
            for q in range(1, 5):
                quarters.append(f"Q{q}{year}")

        self.quarter_combo['values'] = quarters

    def update_quarter_info(self, event=None):
        """Update quarter information display"""
        quarter = self.quarter_var.get()
        if quarter:
            display_name = QuarterManager.get_quarter_display_name(quarter)
            months = QuarterManager.get_quarter_months(quarter)
            month_info = f"Months: {', '.join(months)}"
            self.quarter_info_label.config(text=f"{display_name} | {month_info}")
        else:
            self.quarter_info_label.config(text="")

    def preview_quarter_data(self):
        """Preview data for selected quarter"""
        quarter = self.quarter_var.get()
        if not quarter:
            messagebox.showwarning("Selection Error", "Please select a quarter first.")
            return

        # Fetch invoices for the quarter
        invoices, items_map = JSONGenerator.fetch_invoices_for_quarter(quarter)

        if not invoices:
            messagebox.showinfo("No Data", f"No invoices found for quarter {quarter}.")
            return

        # Show preview window
        PreviewWindow(self, quarter, invoices, items_map)

    def generate_quarterly_json(self):
        """Generate JSON for selected quarter"""
        quarter = self.quarter_var.get()
        if not quarter:
            messagebox.showwarning("Selection Error", "Please select a quarter first.")
            return

        include_hsn = self.include_hsn_var.get()

        # Update status
        self.json_status_label.config(text="Generating JSON...", foreground='#f39c12')
        self.update()

        try:
            # Generate JSON
            json_data, error = JSONGenerator.generate_gstr1_json(quarter, include_hsn)

            if error:
                self.json_status_label.config(text=f"Error: {error}", foreground='#e74c3c')
                messagebox.showerror("Generation Error", error)
                return

            if json_data:
                # Display JSON
                json_string = json.dumps(json_data, indent=2, ensure_ascii=False)
                self.json_output_text.delete('1.0', tk.END)
                self.json_output_text.insert('1.0', json_string)

                # Update status with statistics
                b2b_count = len(json_data.get('b2b', []))
                b2c_count = len(json_data.get('b2cs', []))
                hsn_count = len(json_data.get('hsn', {}).get('data', []))

                status_text = f"JSON generated successfully! B2B: {b2b_count}, B2C: {b2c_count}"
                if include_hsn:
                    status_text += f", HSN: {hsn_count}"

                self.json_status_label.config(text=status_text, foreground='#27ae60')

                messagebox.showinfo("Success", f"JSON generated successfully for {quarter}!")

        except Exception as e:
            error_msg = f"Unexpected error: {str(e)}"
            self.json_status_label.config(text=error_msg, foreground='#e74c3c')
            messagebox.showerror("Generation Error", error_msg)

    def save_json_to_file(self):
        """Save generated JSON to file"""
        json_content = self.json_output_text.get("1.0", tk.END).strip()
        if not json_content:
            messagebox.showwarning("No Data", "Nothing to save. Please generate JSON first.")
            return

        quarter = self.quarter_var.get()
        filing_period = QuarterManager.get_filing_period_for_quarter(quarter) if quarter else ""
        default_filename = f"GSTR1_{quarter}_{filing_period}.json" if quarter else "GSTR1_output.json"

        file_path = filedialog.asksaveasfilename(
            title="Save JSON File",
            defaultextension=".json",
            filetypes=[("JSON files", "*.json"), ("All files", "*.*")],
            initialfile=default_filename
        )

        if file_path:
            try:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(json_content)
                messagebox.showinfo("Success", f"JSON saved to {file_path}")
                self.json_status_label.config(text=f"JSON saved to {file_path}", foreground='#27ae60')
            except Exception as e:
                messagebox.showerror("Save Error", f"Failed to save file: {e}")

# Preview Window for Quarter Data
class PreviewWindow(tk.Toplevel):
    """Window for previewing quarter data before JSON generation"""

    def __init__(self, parent, quarter, invoices, items_map):
        super().__init__(parent)
        self.parent = parent
        self.quarter = quarter
        self.invoices = invoices
        self.items_map = items_map

        self.title(f"Data Preview - {quarter}")
        self.geometry("1000x700")
        self.transient(parent)

        self.create_widgets()
        self.populate_data()

    def create_widgets(self):
        """Create preview widgets"""
        # Main container
        main_frame = ttk.Frame(self, padding=10)
        main_frame.pack(fill='both', expand=True)

        # Title
        title_label = ttk.Label(main_frame,
                               text=f"Data Preview for {QuarterManager.get_quarter_display_name(self.quarter)}",
                               style='Header.TLabel')
        title_label.pack(pady=(0, 20))

        # Statistics frame
        stats_frame = ttk.Frame(main_frame)
        stats_frame.pack(fill='x', pady=(0, 20))

        # Statistics cards
        self.create_stats_cards(stats_frame)

        # Notebook for different views
        self.notebook = ttk.Notebook(main_frame)
        self.notebook.pack(fill='both', expand=True, pady=(0, 10))

        # Invoice list tab
        self.invoice_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.invoice_frame, text='📄 Invoices')

        # Summary tab
        self.summary_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.summary_frame, text='📊 Summary')

        # Create invoice list
        self.create_invoice_list()

        # Create summary view
        self.create_summary_view()

        # Close button
        close_btn = ttk.Button(main_frame, text="Close", command=self.destroy)
        close_btn.pack(pady=(10, 0))

    def create_stats_cards(self, parent):
        """Create statistics cards"""
        b2b_invoices = [inv for inv in self.invoices if inv['invoice_type'] == 'B2B']
        b2c_invoices = [inv for inv in self.invoices if inv['invoice_type'] == 'B2C']

        # Total invoices
        total_card = ModernUI.create_card_frame(parent)
        total_card.pack(side='left', fill='both', expand=True, padx=(0, 10))
        ttk.Label(total_card, text="Total Invoices", font=('Segoe UI', 10, 'bold')).pack()
        ttk.Label(total_card, text=str(len(self.invoices)),
                 font=('Segoe UI', 16, 'bold'), foreground='#3498db').pack()

        # B2B invoices
        b2b_card = ModernUI.create_card_frame(parent)
        b2b_card.pack(side='left', fill='both', expand=True, padx=(5, 5))
        ttk.Label(b2b_card, text="B2B Invoices", font=('Segoe UI', 10, 'bold')).pack()
        ttk.Label(b2b_card, text=str(len(b2b_invoices)),
                 font=('Segoe UI', 16, 'bold'), foreground='#e74c3c').pack()

        # B2C invoices
        b2c_card = ModernUI.create_card_frame(parent)
        b2c_card.pack(side='left', fill='both', expand=True, padx=(5, 0))
        ttk.Label(b2c_card, text="B2C Invoices", font=('Segoe UI', 10, 'bold')).pack()
        ttk.Label(b2c_card, text=str(len(b2c_invoices)),
                 font=('Segoe UI', 16, 'bold'), foreground='#f39c12').pack()

    def create_invoice_list(self):
        """Create invoice list view with customer names"""
        # Invoice treeview with customer name column
        columns = ('Type', 'Invoice No', 'Date', 'Customer (Name | GSTIN)', 'Value', 'Month')
        self.invoice_tree = ttk.Treeview(self.invoice_frame, columns=columns, show='headings', height=20)

        for col in columns:
            self.invoice_tree.heading(col, text=col)

        # Column widths
        self.invoice_tree.column('Type', width=60)
        self.invoice_tree.column('Invoice No', width=120)
        self.invoice_tree.column('Date', width=100)
        self.invoice_tree.column('Customer (Name | GSTIN)', width=300)  # Wider for name + GSTIN
        self.invoice_tree.column('Value', width=100)
        self.invoice_tree.column('Month', width=80)

        # Scrollbars
        v_scroll = ttk.Scrollbar(self.invoice_frame, orient='vertical', command=self.invoice_tree.yview)
        h_scroll = ttk.Scrollbar(self.invoice_frame, orient='horizontal', command=self.invoice_tree.xview)

        self.invoice_tree.configure(yscrollcommand=v_scroll.set, xscrollcommand=h_scroll.set)

        # Grid layout
        self.invoice_tree.grid(row=0, column=0, sticky='nsew')
        v_scroll.grid(row=0, column=1, sticky='ns')
        h_scroll.grid(row=1, column=0, sticky='ew')

        self.invoice_frame.grid_rowconfigure(0, weight=1)
        self.invoice_frame.grid_columnconfigure(0, weight=1)

    def create_summary_view(self):
        """Create summary view"""
        summary_text = tk.Text(self.summary_frame, wrap=tk.WORD, font=('Segoe UI', 10))
        summary_scroll = ttk.Scrollbar(self.summary_frame, orient='vertical', command=summary_text.yview)
        summary_text.configure(yscrollcommand=summary_scroll.set)

        summary_text.pack(side='left', fill='both', expand=True)
        summary_scroll.pack(side='right', fill='y')

        self.summary_text = summary_text

    def populate_data(self):
        """Populate preview data with customer names for validation"""
        # Populate invoice list with customer names
        for invoice in self.invoices:
            invoice_items = self.items_map.get(invoice['id'], [])
            total_value = sum(
                item['taxable_value'] + item['cgst_amount'] + item['sgst_amount'] + item['cess_amount']
                for item in invoice_items
            )

            # Format date
            try:
                inv_date = datetime.strptime(invoice['invoice_date'], '%Y-%m-%d')
                formatted_date = inv_date.strftime('%d-%m-%Y')
            except ValueError:
                formatted_date = invoice['invoice_date']

            # Get customer info with name and GSTIN for validation
            if invoice['invoice_type'] == 'B2B':
                customer_name = DatabaseManager.safe_get_row_value(invoice, 'customer_name', 'Unknown Customer')
                customer_gstin = DatabaseManager.safe_get_row_value(invoice, 'customer_gstin', 'No GSTIN')
                customer_display = f"{customer_name} | {customer_gstin}"
            else:
                customer_display = 'B2C Customer'

            self.invoice_tree.insert('', tk.END, values=(
                invoice['invoice_type'],
                invoice['invoice_number'],
                formatted_date,
                customer_display,
                f"₹{total_value:,.2f}",
                invoice['filing_period']
            ))

        # Populate summary with customer breakdown
        self.populate_summary()

    def populate_summary(self):
        """Populate summary information with customer validation details"""
        b2b_invoices = [inv for inv in self.invoices if inv['invoice_type'] == 'B2B']
        b2c_invoices = [inv for inv in self.invoices if inv['invoice_type'] == 'B2C']

        # Calculate totals
        total_b2b_value = 0
        total_b2c_value = 0
        total_tax = 0

        # Customer-wise breakdown for B2B validation
        customer_breakdown = {}

        for invoice in self.invoices:
            invoice_items = self.items_map.get(invoice['id'], [])
            invoice_value = sum(
                item['taxable_value'] + item['cgst_amount'] + item['sgst_amount'] + item['cess_amount']
                for item in invoice_items
            )

            tax_amount = sum(
                item['cgst_amount'] + item['sgst_amount'] + item['cess_amount']
                for item in invoice_items
            )

            if invoice['invoice_type'] == 'B2B':
                total_b2b_value += invoice_value

                # Group by customer for validation
                customer_gstin = DatabaseManager.safe_get_row_value(invoice, 'customer_gstin', 'UNKNOWN')
                customer_name = DatabaseManager.safe_get_row_value(invoice, 'customer_name', 'Unknown Customer')

                if customer_gstin not in customer_breakdown:
                    customer_breakdown[customer_gstin] = {
                        'name': customer_name,
                        'invoices': 0,
                        'total_value': 0
                    }

                customer_breakdown[customer_gstin]['invoices'] += 1
                customer_breakdown[customer_gstin]['total_value'] += invoice_value
            else:
                total_b2c_value += invoice_value

            total_tax += tax_amount

        # Generate summary text with customer validation
        summary = f"""
QUARTER SUMMARY: {QuarterManager.get_quarter_display_name(self.quarter)}
{'='*80}

INVOICE STATISTICS:
• Total Invoices: {len(self.invoices)}
• B2B Invoices: {len(b2b_invoices)}
• B2C Invoices: {len(b2c_invoices)}

VALUE SUMMARY:
• Total B2B Value: ₹{total_b2b_value:,.2f}
• Total B2C Value: ₹{total_b2c_value:,.2f}
• Total Invoice Value: ₹{(total_b2b_value + total_b2c_value):,.2f}
• Total Tax Amount: ₹{total_tax:,.2f}

B2B CUSTOMER VALIDATION:
{'='*50}
"""

        # Add customer-wise breakdown for validation
        for gstin, details in customer_breakdown.items():
            summary += f"""
Customer: {details['name']}
GSTIN: {gstin}
Invoices: {details['invoices']}
Total Value: ₹{details['total_value']:,.2f}
{'-'*40}"""

        # Month-wise breakdown
        summary += f"""

MONTH-WISE BREAKDOWN:
{'='*30}
"""

        months = QuarterManager.get_quarter_months(self.quarter)
        for month in months:
            month_invoices = [inv for inv in self.invoices if inv['filing_period'] == month]
            month_value = 0

            for invoice in month_invoices:
                invoice_items = self.items_map.get(invoice['id'], [])
                month_value += sum(
                    item['taxable_value'] + item['cgst_amount'] + item['sgst_amount'] + item['cess_amount']
                    for item in invoice_items
                )

            summary += f"• {month}: {len(month_invoices)} invoices, ₹{month_value:,.2f}\n"

        summary += f"""
VALIDATION CHECKLIST:
{'='*25}
✅ Customer Names & GSTIN verified above
✅ Invoice dates within quarter period
✅ Tax calculations appear correct
✅ Ready for JSON generation: {'Yes' if self.invoices else 'No data found'}

NEXT STEPS:
• Review customer details above for accuracy
• Verify GSTIN numbers are correct
• Check invoice amounts match your records
• Generate JSON if everything looks correct
"""

        self.summary_text.insert('1.0', summary)
        self.summary_text.config(state='disabled')

# Customer Form Window
class CustomerFormWindow(tk.Toplevel):
    """Window for adding/editing customer information"""

    def __init__(self, parent, customer_id=None):
        super().__init__(parent)
        self.parent = parent
        self.customer_id = customer_id

        self.title("Edit Customer" if customer_id else "Add New Customer")
        self.geometry("500x600")
        self.transient(parent)
        self.grab_set()

        self.create_widgets()

        if customer_id:
            self.load_customer_data()

    def create_widgets(self):
        """Create form widgets"""
        # Main container
        main_frame = ttk.Frame(self, padding=20)
        main_frame.pack(fill='both', expand=True)

        # Form fields
        self.name_label, self.name_entry = ModernUI.create_form_field(
            main_frame, "Customer Name", is_required=True)

        self.gstin_label, self.gstin_entry = ModernUI.create_form_field(
            main_frame, "GSTIN")

        self.state_label, self.state_entry = ModernUI.create_form_field(
            main_frame, "State Code", is_required=True)

        self.address1_label, self.address1_entry = ModernUI.create_form_field(
            main_frame, "Address Line 1")

        self.address2_label, self.address2_entry = ModernUI.create_form_field(
            main_frame, "Address Line 2")

        self.city_label, self.city_entry = ModernUI.create_form_field(
            main_frame, "City")

        self.pincode_label, self.pincode_entry = ModernUI.create_form_field(
            main_frame, "Pincode")

        self.phone_label, self.phone_entry = ModernUI.create_form_field(
            main_frame, "Phone")

        self.email_label, self.email_entry = ModernUI.create_form_field(
            main_frame, "Email")

        self.contact_label, self.contact_entry = ModernUI.create_form_field(
            main_frame, "Contact Person")

        # Buttons
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill='x', pady=(20, 0))

        save_btn = ttk.Button(button_frame, text="💾 Save", command=self.save_customer)
        save_btn.pack(side='right', padx=(10, 0))

        cancel_btn = ttk.Button(button_frame, text="❌ Cancel", command=self.destroy)
        cancel_btn.pack(side='right')

    def load_customer_data(self):
        """Load existing customer data"""
        customer = DatabaseManager.safe_execute_query(
            "SELECT * FROM Customers WHERE id = ?",
            (self.customer_id,),
            fetch='one'
        )

        if customer:
            # Clear existing entries first
            for entry in [self.name_entry, self.gstin_entry, self.state_entry,
                         self.address1_entry, self.address2_entry, self.city_entry,
                         self.pincode_entry, self.phone_entry, self.email_entry,
                         self.contact_entry]:
                entry.delete(0, tk.END)

            # Load data using safe method
            self.name_entry.insert(0, DatabaseManager.safe_get_row_value(customer, 'name', ''))
            self.gstin_entry.insert(0, DatabaseManager.safe_get_row_value(customer, 'gstin', ''))
            self.state_entry.insert(0, DatabaseManager.safe_get_row_value(customer, 'state_code', ''))
            self.address1_entry.insert(0, DatabaseManager.safe_get_row_value(customer, 'address1', ''))
            self.address2_entry.insert(0, DatabaseManager.safe_get_row_value(customer, 'address2', ''))
            self.city_entry.insert(0, DatabaseManager.safe_get_row_value(customer, 'city', ''))
            self.pincode_entry.insert(0, DatabaseManager.safe_get_row_value(customer, 'pincode', ''))
            self.phone_entry.insert(0, DatabaseManager.safe_get_row_value(customer, 'phone', ''))
            self.email_entry.insert(0, DatabaseManager.safe_get_row_value(customer, 'email', ''))
            self.contact_entry.insert(0, DatabaseManager.safe_get_row_value(customer, 'contact_person', ''))

    def save_customer(self):
        """Save customer data"""
        # Validate required fields
        name = self.name_entry.get().strip()
        state_code = self.state_entry.get().strip()

        if not name:
            messagebox.showerror("Validation Error", "Customer name is required.")
            return

        if not state_code:
            messagebox.showerror("Validation Error", "State code is required.")
            return

        # Validate GSTIN if provided
        gstin = self.gstin_entry.get().strip()
        if gstin:
            is_valid, error_msg = ValidationManager.validate_gstin(gstin)
            if not is_valid:
                messagebox.showerror("Validation Error", f"GSTIN Error: {error_msg}")
                return

        # Prepare data
        customer_data = {
            'name': name,
            'gstin': gstin if gstin else None,
            'state_code': state_code,
            'address1': self.address1_entry.get().strip(),
            'address2': self.address2_entry.get().strip(),
            'city': self.city_entry.get().strip(),
            'pincode': self.pincode_entry.get().strip(),
            'phone': self.phone_entry.get().strip(),
            'email': self.email_entry.get().strip(),
            'contact_person': self.contact_entry.get().strip()
        }

        try:
            if self.customer_id:
                # Update existing customer
                DatabaseManager.execute_query(
                    """UPDATE Customers
                       SET name=?, gstin=?, state_code=?, address1=?, address2=?,
                           city=?, pincode=?, phone=?, email=?, contact_person=?,
                           updated_at=CURRENT_TIMESTAMP
                       WHERE id=?""",
                    (*customer_data.values(), self.customer_id)
                )
                messagebox.showinfo("Success", "Customer updated successfully.")
            else:
                # Create new customer
                DatabaseManager.execute_query(
                    """INSERT INTO Customers
                       (name, gstin, state_code, address1, address2, city, pincode,
                        phone, email, contact_person)
                       VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)""",
                    tuple(customer_data.values())
                )
                messagebox.showinfo("Success", "Customer created successfully.")

            # Refresh parent customer list
            self.parent.refresh_customer_list()
            self.destroy()

        except Exception as e:
            messagebox.showerror("Database Error", f"Failed to save customer: {e}")

# Customer Details Window
class CustomerDetailsWindow(tk.Toplevel):
    """Window for viewing detailed customer information"""

    def __init__(self, parent, customer_data):
        super().__init__(parent)
        self.parent = parent
        self.customer_data = customer_data

        self.title(f"Customer Details - {customer_data['name']}")
        self.geometry("600x500")
        self.transient(parent)

        self.create_widgets()

    def create_widgets(self):
        """Create detail view widgets"""
        # Main container
        main_frame = ttk.Frame(self, padding=20)
        main_frame.pack(fill='both', expand=True)

        # Customer info card
        info_card = ModernUI.create_card_frame(main_frame, "Customer Information")
        info_card.pack(fill='x', pady=(0, 20))

        # Display customer details
        details = [
            ("Name", self.customer_data['name']),
            ("GSTIN", self.customer_data['gstin'] or 'Not provided'),
            ("State Code", self.customer_data['state_code']),
            ("Address", self.format_address()),
            ("Phone", self.customer_data['phone'] or 'Not provided'),
            ("Email", self.customer_data['email'] or 'Not provided'),
            ("Contact Person", self.customer_data['contact_person'] or 'Not provided'),
            ("Status", "Active" if self.customer_data['is_active'] else "Inactive"),
            ("Created", self.customer_data['created_at']),
            ("Last Updated", self.customer_data['updated_at'])
        ]

        for label, value in details:
            detail_frame = ttk.Frame(info_card)
            detail_frame.pack(fill='x', pady=2)

            ttk.Label(detail_frame, text=f"{label}:", font=('Segoe UI', 9, 'bold'),
                     width=15, anchor='w').pack(side='left')
            ttk.Label(detail_frame, text=str(value), font=('Segoe UI', 9)).pack(side='left')

        # Close button
        close_btn = ttk.Button(main_frame, text="Close", command=self.destroy)
        close_btn.pack(pady=(20, 0))

    def format_address(self):
        """Format customer address"""
        address_parts = [
            self.customer_data['address1'],
            self.customer_data['address2'],
            self.customer_data['city'],
            self.customer_data['pincode']
        ]
        return ', '.join(filter(None, address_parts)) or 'Not provided'

if __name__ == '__main__':
    app = EnhancedGSTApp()
    app.mainloop()

#!/usr/bin/env python3
"""
Test script to demonstrate quarter validation functionality
"""

import sys
import os
sys.path.append(os.path.dirname(__file__))

from enhanced_gst_app import QuarterManager, ValidationManager
import csv
from datetime import datetime

def create_mixed_quarter_csv():
    """Create a CSV with invoices from different quarters for testing"""
    print("=== Creating Mixed Quarter Test CSV ===")
    
    # Create test data with invoices from different quarters
    test_data = [
        # Q4 2024 invoices (Jan-Mar 2025) - Target quarter
        {'invoice_type': 'B2B', 'customer_gstin': '27AAMCS0827N1ZU', 'invoice_number': 'Q4-001', 'invoice_date': '15-01-2025', 'taxable_value': '1000.00', 'rate': '5.0', 'cgst_amount': '25.00', 'sgst_amount': '25.00', 'cess_amount': '0.00'},
        {'invoice_type': 'B2B', 'customer_gstin': '27AAMCS0827N1ZU', 'invoice_number': 'Q4-002', 'invoice_date': '20-02-2025', 'taxable_value': '2000.00', 'rate': '5.0', 'cgst_amount': '50.00', 'sgst_amount': '50.00', 'cess_amount': '0.00'},
        {'invoice_type': 'B2B', 'customer_gstin': '27AAKFR4717P1ZI', 'invoice_number': 'Q4-003', 'invoice_date': '10-03-2025', 'taxable_value': '1500.00', 'rate': '5.0', 'cgst_amount': '37.50', 'sgst_amount': '37.50', 'cess_amount': '0.00'},
        
        # Q1 2025 invoices (Apr-Jun 2025) - Wrong quarter
        {'invoice_type': 'B2B', 'customer_gstin': '27AAMCS0827N1ZU', 'invoice_number': 'Q1-001', 'invoice_date': '15-04-2025', 'taxable_value': '3000.00', 'rate': '5.0', 'cgst_amount': '75.00', 'sgst_amount': '75.00', 'cess_amount': '0.00'},
        {'invoice_type': 'B2C', 'customer_gstin': '', 'invoice_number': 'Q1-002', 'invoice_date': '20-05-2025', 'taxable_value': '1200.00', 'rate': '5.0', 'cgst_amount': '30.00', 'sgst_amount': '30.00', 'cess_amount': '0.00'},
        
        # Q3 2024 invoices (Oct-Dec 2024) - Wrong quarter
        {'invoice_type': 'B2B', 'customer_gstin': '27AAKFR4717P1ZI', 'invoice_number': 'Q3-001', 'invoice_date': '15-11-2024', 'taxable_value': '2500.00', 'rate': '5.0', 'cgst_amount': '62.50', 'sgst_amount': '62.50', 'cess_amount': '0.00'},
    ]
    
    # Write to CSV
    with open('mixed_quarter_test.csv', 'w', newline='', encoding='utf-8') as csvfile:
        fieldnames = ['invoice_type', 'customer_gstin', 'invoice_number', 'invoice_date', 'taxable_value', 'rate', 'cgst_amount', 'sgst_amount', 'cess_amount']
        writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
        
        writer.writeheader()
        for row in test_data:
            writer.writerow(row)
    
    print(f"✅ Created mixed_quarter_test.csv with {len(test_data)} invoices")
    print("   - 3 invoices for Q4 2024 (Jan-Mar 2025) ✅")
    print("   - 2 invoices for Q1 2025 (Apr-Jun 2025) ❌")
    print("   - 1 invoice for Q3 2024 (Oct-Dec 2024) ❌")
    print()

def test_quarter_validation_logic():
    """Test the quarter validation logic"""
    print("=== Testing Quarter Validation Logic ===")
    
    target_quarter = "Q42024"  # Jan-Mar 2025
    
    test_dates = [
        # Valid dates for Q4 2024
        ('15-01-2025', True),   # January 2025
        ('28-02-2025', True),   # February 2025
        ('31-03-2025', True),   # March 2025
        
        # Invalid dates (different quarters)
        ('15-04-2025', False),  # Q1 2025
        ('15-07-2025', False),  # Q2 2025
        ('15-10-2024', False),  # Q3 2024
        ('15-12-2024', False),  # Q3 2024
        
        # Invalid date format
        ('2025-01-15', False),  # Wrong format
        ('invalid', False),     # Invalid date
    ]
    
    print(f"Target Quarter: {QuarterManager.get_quarter_display_name(target_quarter)}")
    print(f"Valid Months: {QuarterManager.get_quarter_months(target_quarter)}")
    print()
    
    for date_str, expected_valid in test_dates:
        try:
            # Parse date and get month-year
            inv_date = datetime.strptime(date_str, '%d-%m-%Y')
            month_year = inv_date.strftime('%m%Y')
            actual_quarter = QuarterManager.get_quarter_from_month(month_year)
            
            is_valid = month_year in QuarterManager.get_quarter_months(target_quarter)
            
            status = "✅ VALID" if is_valid else "❌ INVALID"
            result = "✅ PASS" if is_valid == expected_valid else "❌ FAIL"
            
            print(f"{result} {date_str} → {month_year} → {actual_quarter} | {status}")
            
        except ValueError:
            is_valid = False
            status = "❌ INVALID (Bad Format)"
            result = "✅ PASS" if is_valid == expected_valid else "❌ FAIL"
            print(f"{result} {date_str} → Invalid Format | {status}")
    
    print()

def test_csv_validation():
    """Test CSV validation against quarters"""
    print("=== Testing CSV Quarter Validation ===")
    
    target_quarter = "Q42024"
    csv_file = "mixed_quarter_test.csv"
    
    if not os.path.exists(csv_file):
        print(f"❌ CSV file {csv_file} not found. Run create_mixed_quarter_csv() first.")
        return
    
    validation_results = {
        'target_quarter': target_quarter,
        'total_invoices': 0,
        'valid_invoices': 0,
        'mismatched_invoices': []
    }
    
    target_months = QuarterManager.get_quarter_months(target_quarter)
    
    with open(csv_file, 'r', encoding='utf-8') as csvfile:
        reader = csv.DictReader(csvfile)
        
        print(f"Validating against {QuarterManager.get_quarter_display_name(target_quarter)}")
        print(f"Target months: {target_months}")
        print()
        
        for row_num, row in enumerate(reader, start=2):
            validation_results['total_invoices'] += 1
            
            invoice_date = row['invoice_date']
            invoice_number = row['invoice_number']
            
            try:
                inv_date = datetime.strptime(invoice_date, '%d-%m-%Y')
                month_year = inv_date.strftime('%m%Y')
                actual_quarter = QuarterManager.get_quarter_from_month(month_year)
                
                if month_year in target_months:
                    validation_results['valid_invoices'] += 1
                    print(f"✅ Row {row_num}: {invoice_number} ({invoice_date}) → {actual_quarter}")
                else:
                    validation_results['mismatched_invoices'].append({
                        'row': row_num,
                        'invoice_number': invoice_number,
                        'invoice_date': invoice_date,
                        'actual_quarter': actual_quarter
                    })
                    print(f"❌ Row {row_num}: {invoice_number} ({invoice_date}) → {actual_quarter} (Expected: {target_quarter})")
                    
            except ValueError:
                validation_results['mismatched_invoices'].append({
                    'row': row_num,
                    'invoice_number': invoice_number,
                    'invoice_date': invoice_date,
                    'actual_quarter': 'Invalid Date'
                })
                print(f"❌ Row {row_num}: {invoice_number} ({invoice_date}) → Invalid Date Format")
    
    print()
    print("VALIDATION SUMMARY:")
    print(f"📊 Total Invoices: {validation_results['total_invoices']}")
    print(f"✅ Valid for {target_quarter}: {validation_results['valid_invoices']}")
    print(f"❌ Mismatched: {len(validation_results['mismatched_invoices'])}")
    
    if validation_results['mismatched_invoices']:
        print("\nMISMATCHED INVOICES:")
        for mismatch in validation_results['mismatched_invoices']:
            print(f"  • {mismatch['invoice_number']} ({mismatch['invoice_date']}) belongs to {mismatch['actual_quarter']}")
    
    print()

def demonstrate_workflow():
    """Demonstrate the complete quarter validation workflow"""
    print("=== Quarter Validation Workflow Demo ===")
    print()
    
    print("STEP 1: User selects target quarter")
    print("   → User selects Q4 2024 (Jan-Mar 2025) from dropdown")
    print("   → System shows: 'Q4 2024 (Jan-Mar 2025) | Months: 012025, 022025, 032025'")
    print()
    
    print("STEP 2: User uploads CSV file")
    print("   → User selects mixed_quarter_test.csv")
    print("   → User enables 'Validate dates against target quarter'")
    print("   → User clicks 'Upload and Process CSV'")
    print()
    
    print("STEP 3: System validates quarters")
    print("   → System scans all invoice dates in CSV")
    print("   → Identifies invoices that don't belong to Q4 2024")
    print("   → Shows validation dialog with results")
    print()
    
    print("STEP 4: User reviews validation results")
    print("   → Dialog shows: '3 valid, 3 mismatched invoices'")
    print("   → Lists mismatched invoices with their actual quarters")
    print("   → User can choose to proceed or cancel")
    print()
    
    print("STEP 5: Data processing")
    print("   → If user proceeds: All invoices imported with flags")
    print("   → Mismatched invoices marked for review")
    print("   → Upload summary shows quarter mismatches")
    print()
    
    print("STEP 6: JSON generation")
    print("   → When generating JSON for Q4 2024")
    print("   → Only invoices from Jan-Mar 2025 included")
    print("   → Mismatched invoices excluded from this quarter's JSON")
    print("   → User can generate separate JSONs for other quarters")

def main():
    """Run all quarter validation tests"""
    print("Enhanced GST App - Quarter Validation Test Suite")
    print("=" * 70)
    print()
    
    create_mixed_quarter_csv()
    test_quarter_validation_logic()
    test_csv_validation()
    demonstrate_workflow()
    
    print("=" * 70)
    print("✅ Quarter validation testing completed!")
    print()
    print("KEY BENEFITS:")
    print("• Prevents mixing invoices from different quarters")
    print("• Validates data before import")
    print("• Shows clear validation results")
    print("• Allows user to make informed decisions")
    print("• Ensures accurate quarterly GST filing")
    print()
    print("NEXT STEPS:")
    print("1. Upload mixed_quarter_test.csv through the enhanced app")
    print("2. Select Q4 2024 as target quarter")
    print("3. Enable quarter validation")
    print("4. See the validation dialog in action!")

if __name__ == "__main__":
    main()

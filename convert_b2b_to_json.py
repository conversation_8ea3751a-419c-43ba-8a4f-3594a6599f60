#!/usr/bin/env python3
"""
Convert b2b,sez,de.csv to GSTR-1 JSON format
"""

import csv
import json
from datetime import datetime
from collections import defaultdict

def parse_place_of_supply(pos_string):
    """Extract state code from place of supply string"""
    if pos_string and '-' in pos_string:
        return pos_string.split('-')[0]
    return "27"  # Default to Maharashtra

def calculate_tax_amounts(taxable_value, rate):
    """Calculate CGST and SGST amounts"""
    tax_amount = (taxable_value * rate) / 100
    cgst = tax_amount / 2
    sgst = tax_amount / 2
    return cgst, sgst

def convert_csv_to_gstr1_json():
    """Convert the CSV file to GSTR-1 JSON format"""
    
    # Read CSV data
    invoices_data = []
    
    with open('b2b,sez,de.csv', 'r', encoding='utf-8') as csvfile:
        reader = csv.DictReader(csvfile)
        
        for row in reader:
            # Skip empty rows
            if not row.get('GSTIN/UIN of Recipient'):
                continue
                
            # Parse and clean data
            invoice_data = {
                'customer_gstin': row['GSTIN/UIN of Recipient'].strip(),
                'customer_name': row['Receiver Name'].strip(),
                'invoice_number': row['Invoice Number'].strip(),
                'invoice_date': row['Invoice date'].strip(),
                'invoice_value': float(row['Invoice Value']) if row['Invoice Value'] else 0,
                'place_of_supply': parse_place_of_supply(row['Place Of Supply']),
                'rate': float(row['Rate']) if row['Rate'] else 5.0,
                'taxable_value': float(row['Taxable Value']) if row['Taxable Value'] else 0,
                'cess_amount': float(row['Cess Amount']) if row['Cess Amount'] else 0
            }
            
            # Calculate tax amounts
            cgst, sgst = calculate_tax_amounts(invoice_data['taxable_value'], invoice_data['rate'])
            invoice_data['cgst_amount'] = cgst
            invoice_data['sgst_amount'] = sgst
            
            invoices_data.append(invoice_data)
    
    print(f"Loaded {len(invoices_data)} invoices from CSV")
    
    # Group invoices by customer GSTIN
    customers = defaultdict(list)
    
    for invoice in invoices_data:
        customers[invoice['customer_gstin']].append(invoice)
    
    # Build GSTR-1 JSON structure
    gstr1_json = {
        "gstin": "27AABFE3477G1ZG",  # Your supplier GSTIN
        "fp": "062025",  # Q1 2025 filing period (June 2025)
        "version": "GST3.2",
        "hash": "hash",
        "b2b": []
    }
    
    # Process each customer
    for customer_gstin, customer_invoices in customers.items():
        customer_data = {
            "ctin": customer_gstin,
            "inv": []
        }
        
        # Process each invoice for this customer
        for invoice in customer_invoices:
            # Format invoice date
            try:
                inv_date = datetime.strptime(invoice['invoice_date'], '%d-%m-%Y')
                formatted_date = inv_date.strftime('%d-%m-%Y')
            except ValueError:
                formatted_date = invoice['invoice_date']
            
            # Calculate total invoice value
            total_value = invoice['taxable_value'] + invoice['cgst_amount'] + invoice['sgst_amount'] + invoice['cess_amount']
            
            invoice_obj = {
                "inum": invoice['invoice_number'],
                "idt": formatted_date,
                "val": round(total_value, 2),
                "pos": invoice['place_of_supply'],
                "rchrg": "N",  # No reverse charge
                "inv_typ": "R",  # Regular invoice
                "itms": [
                    {
                        "num": 1,
                        "itm_det": {
                            "txval": round(invoice['taxable_value'], 2),
                            "rt": round(invoice['rate'], 2),
                            "camt": round(invoice['cgst_amount'], 2),
                            "samt": round(invoice['sgst_amount'], 2),
                            "csamt": round(invoice['cess_amount'], 2)
                        }
                    }
                ]
            }
            
            customer_data["inv"].append(invoice_obj)
        
        gstr1_json["b2b"].append(customer_data)
    
    # Add HSN summary (optional)
    hsn_summary = {
        "data": [
            {
                "num": 1,
                "hsn_sc": "998892",  # Default HSN for services
                "desc": "Professional Services",
                "uqc": "NOS",
                "qty": len(invoices_data),
                "txval": round(sum(inv['taxable_value'] for inv in invoices_data), 2),
                "rt": 5.0,
                "iamt": 0.0,
                "camt": round(sum(inv['cgst_amount'] for inv in invoices_data), 2),
                "samt": round(sum(inv['sgst_amount'] for inv in invoices_data), 2),
                "csamt": round(sum(inv['cess_amount'] for inv in invoices_data), 2)
            }
        ]
    }
    
    gstr1_json["hsn"] = hsn_summary
    
    return gstr1_json, invoices_data

def main():
    """Main function to convert CSV and save JSON"""
    print("Converting b2b,sez,de.csv to GSTR-1 JSON format...")
    print("=" * 60)
    
    try:
        # Convert CSV to JSON
        gstr1_json, invoices_data = convert_csv_to_gstr1_json()
        
        # Save to file
        with open('gstr1.json', 'w', encoding='utf-8') as jsonfile:
            json.dump(gstr1_json, jsonfile, indent=2, ensure_ascii=False)
        
        print(f"✅ Successfully created gstr1.json")
        print()
        
        # Show summary
        print("SUMMARY:")
        print(f"📊 Total Invoices: {len(invoices_data)}")
        print(f"👥 Unique Customers: {len(gstr1_json['b2b'])}")
        print(f"💰 Total Taxable Value: ₹{sum(inv['taxable_value'] for inv in invoices_data):,.2f}")
        print(f"🏛️ Total Tax Amount: ₹{sum(inv['cgst_amount'] + inv['sgst_amount'] for inv in invoices_data):,.2f}")
        print()
        
        # Show customer breakdown
        print("CUSTOMER BREAKDOWN:")
        for customer in gstr1_json['b2b']:
            customer_gstin = customer['ctin']
            invoice_count = len(customer['inv'])
            total_value = sum(inv['val'] for inv in customer['inv'])
            
            # Get customer name from first invoice
            customer_name = "Unknown"
            for inv_data in invoices_data:
                if inv_data['customer_gstin'] == customer_gstin:
                    customer_name = inv_data['customer_name']
                    break
            
            print(f"• {customer_name}")
            print(f"  GSTIN: {customer_gstin}")
            print(f"  Invoices: {invoice_count}")
            print(f"  Total Value: ₹{total_value:,.2f}")
            print()
        
        # Show filing period info
        print("FILING INFORMATION:")
        print(f"📅 Filing Period: {gstr1_json['fp']} (Q1 2025 - Apr to Jun 2025)")
        print(f"🏢 Supplier GSTIN: {gstr1_json['gstin']}")
        print(f"📋 JSON Version: {gstr1_json['version']}")
        print()
        
        print("✅ gstr1.json is ready for GST portal upload!")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
